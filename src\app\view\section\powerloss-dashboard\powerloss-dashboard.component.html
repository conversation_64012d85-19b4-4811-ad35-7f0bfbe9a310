<p class="header-info">{{ projectInfo.project_name }} ({{ projectInfo.date }})</p>
<div class="container-fluid" style="margin-top: 80px">
  <p class="card-heading" *ngIf="isDataLoading">📊 Generating insights...</p>
  <p class="card-heading" *ngIf="!data && !isDataLoading">
    ⚠️ No insights available yet. <br />
    Data will appear when processed.
  </p>
  <div class="my-3" [hidden]="!data || isDataLoading">
    <mat-accordion>
      <ng-container *ngIf="data && !isDataLoading">
        <mat-expansion-panel [expanded]="true">
          <mat-expansion-panel-header>
            <mat-panel-title>
              <div class="d-flex justify-content-between align-items-center w-100">
                <h2 class="font-weight-bold m-0">Overview & Summary</h2>
                <div class="d-flex justify-content-between align-items-center gap-3 m-2">
                  <button
                    mat-mini-fab
                    matTooltip="Download Excel Report"
                    (click)="downloadReport(excel_report)">
                    <mat-icon>description</mat-icon>
                  </button>
                  <button
                    mat-mini-fab
                    matTooltip="Download Analysis PDF Report"
                    (click)="downloadReport(analysis_pdf_report)">
                    <mat-icon>picture_as_pdf</mat-icon>
                  </button>
                  <button
                    mat-mini-fab
                    matTooltip="Download Reference PDF Report"
                    (click)="downloadReport(reference_pdf_report)">
                    <mat-icon> table_chart</mat-icon>
                  </button>
                </div>
              </div>
            </mat-panel-title>
          </mat-expansion-panel-header>
          <div class="row">
            <div class="col-md-4 mb-3">
              <div class="card shadow h-100">
                <div class="card-body">
                  <h3 class="card-title text-left font-weight-bold">Total Defects</h3>
                  <h2 class="card-text">{{ data?.overall_summary.total_defects | number }}</h2>
                </div>
              </div>
            </div>
            <div class="col-md-4 mb-3">
              <div class="card shadow h-100">
                <div class="card-body">
                  <h3 class="card-title text-left font-weight-bold">Total Power Loss</h3>
                  <h2 class="card-text">
                    {{ data?.overall_summary.total_power_loss | number: '1.0-0' }} W
                  </h2>
                </div>
              </div>
            </div>
            <div class="col-md-4 mb-3">
              <div class="card shadow h-100">
                <div class="card-body">
                  <h3 class="card-title text-left font-weight-bold">Avg. Power Loss/Defect</h3>
                  <h2 class="card-text">
                    {{ data?.overall_summary.average_power_loss | number: '1.0-0' }} W
                  </h2>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-3 mb-3">
              <div class="card">
                <div class="card-body">
                  <h4 class="card-title text-left">Most Common Defect</h4>
                  <h3 class="m-0 font-weight-bold">
                    {{
                      formatDefectName(
                        (data?.overall_summary.defect_counts | keyvalue: compareData)?.[0]?.key
                      )
                    }}
                  </h3>
                  <small class="text-muted">
                    {{ (data?.overall_summary.defect_counts | keyvalue: compareData)?.[0]?.value }}
                    occurrences
                  </small>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card">
                <div class="card-body">
                  <h4 class="card-title text-left">Highest Power Loss Defect</h4>
                  <h3 class="m-0 font-weight-bold">
                    {{
                      formatDefectName(
                        (data?.power_loss_analysis | keyvalue: compareData)?.[0]?.key
                      )
                    }}
                  </h3>
                  <small class="text-muted">
                    {{
                      (data?.power_loss_analysis | keyvalue: compareData)?.[0]?.value?.sum?.toFixed(
                        2
                      ) || '0.00'
                    }}
                    W total
                  </small>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card">
                <div class="card-body">
                  <h4 class="card-title text-left">Most Critical Block</h4>
                  <h3 class="m-0 font-weight-bold">
                    {{
                      formatDefectName(
                        (data?.overall_summary.defect_by_block | keyvalue: compareData)?.[0]?.key
                      )
                    }}
                  </h3>
                  <small class="text-muted">
                    {{
                      (data?.overall_summary.defect_by_block | keyvalue: compareData)?.[0]?.value
                    }}
                    defects
                  </small>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card">
                <div class="card-body">
                  <h4 class="card-title text-left">Highest Temp. Variation</h4>
                  <h3 class="m-0 font-weight-bold">
                    {{
                      formatDefectName(
                        (data?.temperature_analysis?.max_delta_t | keyvalue: compareData)?.[0]?.key
                      )
                    }}
                  </h3>
                  <small class="text-muted">
                    {{
                      (data?.temperature_analysis?.max_delta_t
                        | keyvalue: compareData)?.[0]?.value?.toFixed(2) || '0.00'
                    }}°C
                  </small>
                </div>
              </div>
            </div>
          </div>
        </mat-expansion-panel>
        <mat-expansion-panel>
          <mat-expansion-panel-header>
            <mat-panel-title>
              <h2 class="font-weight-bold m-0">Analytics & Visualizations</h2>
            </mat-panel-title>
          </mat-expansion-panel-header>
          <div class="row">
            <div class="col-md-12 mb-3">
              <div class="card shadow h-100">
                <div class="card-body">
                  <div class="d-flex justify-content-between align-items-center my-2">
                    <mat-button-toggle-group
                      name="normalizationMode"
                      [(ngModel)]="normalizationMode"
                      (ngModelChange)="onDefectBubbleTypeChange()">
                      <mat-button-toggle value="mean">Mean Loss</mat-button-toggle>
                      <mat-button-toggle value="total">Total Loss</mat-button-toggle>
                    </mat-button-toggle-group>
                    <mat-button-toggle-group
                      name="normalizationMode"
                      [(ngModel)]="scaleMode"
                      (ngModelChange)="onDefectBubbleTypeChange()">
                      <mat-button-toggle value="delta">Max ΔTemp</mat-button-toggle>
                      <mat-button-toggle value="avg">Avg. Temp</mat-button-toggle>
                    </mat-button-toggle-group>
                  </div>
                  <ng-container *ngIf="showBubbleChart">
                    <plotly-plot
                      [data]="powerLossScatter?.data"
                      [layout]="powerLossScatter?.layout"
                      [config]="powerLossScatter?.config"></plotly-plot>
                  </ng-container>
                </div>
              </div>
            </div>
            <div class="col-md-12 mb-3">
              <div class="card shadow h-100">
                <div class="card-body">
                  <plotly-plot
                    [data]="defectDistributionChart?.data"
                    [layout]="defectDistributionChart?.layout"
                    [config]="defectDistributionChart?.config"></plotly-plot>
                </div>
              </div>
            </div>
            <div class="col-md-12 mb-3">
              <div class="card shadow h-100">
                <div class="card-body">
                  <plotly-plot
                    [data]="threeDScatterChart?.data"
                    [layout]="threeDScatterChart?.layout"
                    [config]="threeDScatterChart?.config">
                  </plotly-plot>
                </div>
              </div>
            </div>
            <div class="col-md-12 mb-3">
              <div class="card shadow h-100">
                <div class="card-body">
                  <plotly-plot
                    [data]="parallelChart?.data"
                    [layout]="parallelChart?.layout"
                    [config]="parallelChart?.config">
                  </plotly-plot>
                </div>
              </div>
            </div>
            <div class="col-md-12 mb-3">
              <div class="card shadow h-100">
                <div class="card-body">
                  <plotly-plot
                    [data]="temperatureHeatmap?.data"
                    [layout]="temperatureHeatmap?.layout"
                    [config]="temperatureHeatmap?.config"></plotly-plot>
                </div>
              </div>
            </div>
            <div class="col-md-12 mb-3">
              <div class="card shadow h-100">
                <div class="card-body">
                  <mat-button-toggle-group
                    name="selectedChartType"
                    [(ngModel)]="selectedChartType"
                    (ngModelChange)="onBoxChartTypeChange()">
                    <mat-button-toggle value="violin">Violin Plot</mat-button-toggle>
                    <mat-button-toggle value="box">Box Plot</mat-button-toggle>
                  </mat-button-toggle-group>
                  <ng-container *ngIf="showBoxChart">
                    <plotly-plot
                      [data]="boxPlotChart?.data"
                      [layout]="boxPlotChart?.layout"
                      [config]="boxPlotChart?.config">
                    </plotly-plot>
                  </ng-container>
                </div>
              </div>
            </div>
            <div class="col-md-12 mb-3">
              <div class="card shadow h-100">
                <div class="card-body">
                  <mat-button-toggle-group
                    name="defectChartType"
                    [(ngModel)]="selectedDefectChartType"
                    (ngModelChange)="onDefectChartTypeChange()">
                    <mat-button-toggle value="dual">Dual View</mat-button-toggle>
                    <mat-button-toggle value="mean">Mean Only</mat-button-toggle>
                    <mat-button-toggle value="total">Total Only</mat-button-toggle>
                  </mat-button-toggle-group>
                  <ng-container *ngIf="showDefectChart">
                    <plotly-plot
                      [data]="defectSummaryChart?.data"
                      [layout]="defectSummaryChart?.layout"
                      [config]="defectSummaryChart?.config"></plotly-plot>
                  </ng-container>
                </div>
              </div>
            </div>
            <div class="col-md-12 mb-3">
              <div class="card shadow h-100">
                <div class="card-body">
                  <plotly-plot
                    [data]="blockDefectsChart?.data"
                    [layout]="blockDefectsChart?.layout"
                    [config]="blockDefectsChart?.config"></plotly-plot>
                </div>
              </div>
            </div>
            <div class="col-md-12 mb-3">
              <div class="card shadow h-100">
                <div class="card-body">
                  <plotly-plot
                    [data]="defectStandardDeviationChart?.data"
                    [layout]="defectStandardDeviationChart?.layout"
                    [config]="defectStandardDeviationChart?.config"></plotly-plot>
                </div>
              </div>
            </div>
            <div class="col-md-12 mb-3">
              <div class="card shadow h-100">
                <div class="card-body">
                  <mat-button-toggle-group
                    name="chartType"
                    [(ngModel)]="selectedTempChartType"
                    (ngModelChange)="onTempChartTypeChange()">
                    <mat-button-toggle value="group">Bar</mat-button-toggle>
                    <mat-button-toggle value="stack">Stacked</mat-button-toggle>
                    <mat-button-toggle value="line">Line</mat-button-toggle>
                  </mat-button-toggle-group>
                  <ng-container *ngIf="showTempChart">
                    <plotly-plot
                      [data]="temperatureComparisonChart?.data"
                      [layout]="temperatureComparisonChart?.layout"
                      [config]="temperatureComparisonChart?.config"></plotly-plot>
                  </ng-container>
                </div>
              </div>
            </div>
          </div>
        </mat-expansion-panel>
        <mat-expansion-panel>
          <mat-expansion-panel-header>
            <mat-panel-title>
              <h2 class="font-weight-bold m-0">Key Defects & Highlights</h2>
            </mat-panel-title>
          </mat-expansion-panel-header>
          <div class="row">
            <div class="col-md-3 mb-3" *ngFor="let defect of criticalDefects">
              <div class="card" style="border: 1px solid darkcyan">
                <div class="card-body">
                  <h4 class="card-title font-weight-bold">
                    {{ formatDefectName(defect.name) }}
                  </h4>
                  <div class="text-muted mt-2">
                    <div>
                      <span class="font-weight-bold">Count:</span>
                      {{ defect.count }}
                    </div>
                    <div>
                      <span class="font-weight-bold">Mean Loss:</span>
                      {{ defect.value | number: '1.0-0' }} W
                    </div>
                    <div>
                      <span class="font-weight-bold">Total Loss:</span>
                      {{ defect.total | number: '1.0-0' }} W
                    </div>
                    <div>
                      <span class="font-weight-bold">Std Dev:</span>
                      {{ defect.std | number: '1.0-0' }}
                    </div>
                    <div>
                      <span class="font-weight-bold">Max ΔTemp:</span>
                      {{ defect.deltaT | number: '1.0-0' }} °C
                    </div>
                    <div>
                      <span class="font-weight-bold">Avg. Temp:</span>
                      {{ defect.avgT | number: '1.0-0' }} °C
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </mat-expansion-panel>
      </ng-container>
      <mat-expansion-panel>
        <mat-expansion-panel-header>
          <mat-panel-title>
            <h2 class="font-weight-bold m-0">In-Depth Defect Analysis</h2>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <table mat-table [dataSource]="dataSource" matSort>
          <ng-container *ngFor="let column of displayedColumns" [matColumnDef]="column">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ formatColumnName(column) }}
            </th>
            <td mat-cell *matCellDef="let element">{{ element[column] || '-' }}</td>
          </ng-container>
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
        <mat-paginator
          showFirstLastButtons
          [pageSizeOptions]="[10, 25, 50, 100]"
          [disabled]="!dataSource?.data?.length">
        </mat-paginator>
      </mat-expansion-panel>
    </mat-accordion>
  </div>
</div>
