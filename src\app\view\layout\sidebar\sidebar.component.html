<div id="compact-sidebar">
  <nav class="fix-top">
    <mat-card class="app-logo">
      <img class="p-1" src="{{ sidebar_logo }}" *ngIf="sidebar_logo" />
    </mat-card>
    <li
      [routerLink]="['/app', 'home']"
      class="list-item"
      matTooltip="Home"
      matTooltipPosition="right"
      [style.background-color]="selectedIcon === 'home' ? 'lightslategrey' : 'transparent'"
      (click)="fixIcon('home')">
      <mat-icon class="menu-icon">home</mat-icon>
      <span class="menu-text">Home</span>
    </li>
    <ng-container *appHasRole="['admin', 'manager']">
      <li
        [routerLink]="['/app', 'project', 'create']"
        class="list-item"
        matTooltip="Create New Project"
        matTooltipPosition="right"
        [style.background-color]="selectedIcon === 'create' ? 'lightslategrey' : 'transparent'"
        (click)="fixIcon('create')">
        <mat-icon class="menu-icon">create_new_folder</mat-icon>
        <span class="menu-text">Create Project</span>
      </li>
    </ng-container>
    <li
      [routerLink]="['/app', 'project', 'all']"
      class="list-item"
      matTooltip="All Projects"
      matTooltipPosition="right"
      [style.background-color]="selectedIcon === 'Projects' ? 'lightslategrey' : 'transparent'"
      (click)="fixIcon('Projects')">
      <mat-icon class="menu-icon">space_dashboard</mat-icon>
      <span class="menu-text">Projects</span>
    </li>
    <li
      [routerLink]="['/map']"
      *ngIf="isAnalytics"
      class="list-item"
      matTooltip="Map Page"
      matTooltipPosition="right"
      [style.background-color]="selectedIcon === 'map' ? 'lightslategrey' : 'transparent'"
      (click)="fixIcon('map')">
      <mat-icon class="menu-icon">map</mat-icon>
      <span class="menu-text">Map</span>
    </li>
  </nav>
  <nav class="fix-bottom">
    <ng-container *appHasRole="'admin'">
      <li
        [routerLink]="['/app', 'manage-users']"
        class="list-item"
        matTooltip="Manage Users"
        matTooltipPosition="right"
        [style.background-color]="
          selectedIcon === 'manage-users' ? 'lightslategrey' : 'transparent'
        "
        (click)="fixIcon('manage-users')">
        <mat-icon class="menu-icon">supervised_user_circle</mat-icon>
        <span class="menu-text">Manage Users</span>
      </li>
    </ng-container>
    <li
      [routerLink]="['/app', 'my-profile']"
      class="list-item"
      matTooltip="My Profile"
      matTooltipPosition="right"
      [style.background-color]="selectedIcon === 'my-profile' ? 'lightslategrey' : 'transparent'"
      (click)="fixIcon('my-profile')">
      <mat-icon class="menu-icon">account_circle</mat-icon>
      <span class="menu-text">Profile</span>
    </li>
    <li
      class="list-item"
      matTooltip="Help Center"
      matTooltipPosition="right"
      [style.background-color]="selectedIcon === 'Help Center' ? 'lightslategrey' : 'transparent'"
      (click)="fixIcon('Help Center'); openHelpCenter()">
      <mat-icon class="menu-icon">question_answer</mat-icon>
      <span class="menu-text">Help Center</span>
    </li>
    <li
      (click)="logout()"
      class="list-item"
      matTooltip="Log Out"
      matTooltipPosition="right"
      [style.background-color]="selectedIcon === 'Log' ? 'lightslategrey' : 'transparent'"
      (click)="fixIcon('Log')">
      <mat-icon class="menu-icon">logout</mat-icon>
      <span class="menu-text">Log Out</span>
    </li>
  </nav>
</div>
