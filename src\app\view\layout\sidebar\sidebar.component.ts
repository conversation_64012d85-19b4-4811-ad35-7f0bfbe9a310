import { AuthService } from '@/controller/auth.service';
import { SharedDataService } from '@/controller/shared-data.service';
import { UserService } from '@/controller/user.service';
import { HttpService } from '@/view/map-section/services-map/http.service';
import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css'],
})
export class SidebarComponent implements OnInit {
  isAnalytics = false;
  sidebar_logo = '';
  help_center = environment.help_center;
  selectedIcon: string = null;

  constructor(
    private sharedDataService: SharedDataService,
    private userService: UserService,
    private _http: HttpService,
    private authService: AuthService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.userService.sidebarLogo$.subscribe((logo: string) => {
      this.sidebar_logo = logo;
    });
    this._http.get_analytics_icon().subscribe(data => {
      this.isAnalytics = data;
    });
  }
  fixIcon(option) {
    this.selectedIcon = option;
  }
  logout() {
    this.authService.logout();
    this.toastr.success('You have been successfully logged out', 'Logout');
    this.sharedDataService.clearData();
  }
  openHelpCenter() {
    window.open(this.help_center, '_blank');
  }
}
