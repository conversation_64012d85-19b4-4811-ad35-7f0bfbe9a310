.fix-bottom {
  list-style: none;
  position: fixed;
  bottom: 0px;
  width: 70px;
  display: flex;
  flex-direction: column;
  height: 275px;
  justify-content: space-evenly;
  align-items: center;
}
.fix-top {
  position: relative;
  width: 70px;
  list-style: none;
  display: flex;
  flex-direction: column;
  height: 275px;
  justify-content: space-evenly;
  align-items: center;
}
.list-item {
  width: 60px;
  height: fit-content;
  padding: 5px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
}

.list-item:hover {
  background-color: lightslategrey !important;
  cursor: pointer;
}

.fixed-top {
  position: relative;
}

.menu-active {
  border-left: 5px solid var(--primary);
  background-color: #0060fe1c;
  color: var(--primary) !important;
}

.extended-sidebar {
  width: 180px;
}

.menu-section {
  position: absolute;
  right: 30px;
}

.menu-section .icon-category {
  position: relative;
  top: 8px;
}

.menu-section .icon-category .account {
  font-size: 13px !important;
}

.menu-section .icon-category .account .menu-btn {
  font-size: 14px !important;
}

.menu-toggle {
  position: absolute;
  right: 15px;
}

.sidemenu-header {
  padding: 10px 12px;
}

.menu-icon {
  font-size: 25px;
  color: var(--white);
}
.menu-icon:hover {
  font-size: 27px;
  font-weight: 400;
}
.app-logo {
  margin-bottom: 3px;
  width: 60px;
}
.logo {
  width: 12.5rem;
  height: 3.125rem;
  padding: 4px;
}
.menu-text {
  font-size: 10px;
  font-weight: 600;
  text-decoration: none;
  color: var(--white);
  text-align: center;
}
.menu-text:hover {
  text-decoration: none;
  cursor: pointer;
}
a {
  text-decoration: none;
}

.border-btn {
  border: 1px solid;
}

.compact-menu-item {
  position: relative;
}

.compact-menu-icon {
  position: fixed;
  left: 30px;
  width: 20px;
  height: 20px;
  color: var(--white);
  margin: -14px;
}

::ng-deep .mat-drawer-side {
  border-right: solid 0px rgba(0, 0, 0, 0.12) !important;
  box-shadow: 0px 3px 3px var(--shadow) !important;
  background-color: var(--nav) !important;
}

::ng-deep .mat-list-base .mat-subheader {
  color: var(--white);
}

#compact-sidebar {
  display: flex;
}

#extended-sidebar {
  display: none;
}

.menu-name {
  font-size: 10px;
  padding: 2px;
  color: var(--white);
  list-style: none;
}

.menu-name1 {
  font-size: 10px;
  padding: 2px;
  color: var(--white);
  list-style: none;
  margin-left: 6px;
  position: relative;
  top: 2px;
}
