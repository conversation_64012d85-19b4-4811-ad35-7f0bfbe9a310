import { ApiConfigService } from '@/controller/api-config.service';
import { UserService } from '@/controller/user.service';
import { Drawtool, DrawtoolC } from '@/interfaces/drawtool';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import {
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  OnInit,
  Output,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import '@optometristpritam/leaflet-height/leaflet-elevation/src/index.js';
import 'leaflet';
import 'leaflet-draw';
import 'leaflet-kml';
import { ToastrService } from 'ngx-toastr';
import 'src/assets/plugins/leaflet.edgebuffer.js';
import 'src/assets/plugins/leaflet.history.js';
import 'src/assets/plugins/leaflet.twofingerzoom.js';
import { environment } from 'src/environments/environment';
import { HttpService } from './services-map/http.service';
import { SidebarComponent } from './sidebar/sidebar.component';
import { AoiDialogComponent } from './sub-components/aoi-dialog/aoi-dialog.component';
declare const L: any;

@Component({
  selector: 'app-map-section',
  templateUrl: './map-section.component.html',
  styleUrls: ['./map-section.component.css'],
})
export class MapSectionComponent implements OnInit {
  @ViewChild(SidebarComponent) sidebarComponent!: SidebarComponent;
  @ViewChild('mapContainer', { static: false }) mapContainer!: ElementRef;
  map!: any;
  logo: string = '';
  currentTime: string = '';
  terrain_tile_layer: string = environment.terrain_tile_layer;
  sattelite_tile_layer: string = environment.sattelite_tile_layer;
  @Input() item: any;
  weather_show: boolean = false;
  storedDateValue: boolean;
  thermal_layer;
  cad_layer;
  satellite_layer;
  popupDesc = null;
  track: any = null;
  allDefects = [];
  gb_layer: any = null;
  popupKml: any;
  popup_card_visibility: boolean;
  popup_card_visibility_cadestral: boolean;
  popup_card_visibility_grading: boolean;
  Avg_temp: any;
  summaryState: string;
  removekml_list = [];
  removekml_list_inv = [];
  Project_data: any;
  Project_data_thermo: any;
  Project_data_grad: any;
  Project_data_values: any;
  Project_data_inverter_values: any;
  project_name: any;
  Project_data_inverter: any;
  ortho_file_location: any;
  base_ortho_layer: any;
  all_anomolies_clicked = true;
  graph_pie;
  date: any;
  summ: any;
  @Output() current_date_event: EventEmitter<any> = new EventEmitter<any>();
  grading_defect: any;
  @Output() grading_defect_event: EventEmitter<any> = new EventEmitter<any>();
  center: any;
  Completed_date_array: any = [];
  lat: any;
  long: any;
  kml_file_location: any;
  poly: any;
  polies = [];
  descObj: any;
  descObj_cadestral: any;
  table_no: any;
  thermal_location: any;
  cad_file_location: any;
  current_kml_data: any;
  Description_cadestral: any;
  Document: any;
  Survey_No: any;
  Document_link: any;
  popup_opened: boolean;
  terrainview: any;
  satellite = '../../../assets/images/satellite.jpg';
  default = '../../../assets/images/default.jpg';
  DTM = '../../../assets/images/DroneData/DSM.png';
  Slope = '../../../assets/images/DroneData/Slope.png';
  Orthomosaic = '../../../assets/images/DroneData/Orthomosaic.png';
  Clear = '../../../assets/images/Clear.png';
  CAD = '../../../assets/images/DroneData/CAD.png';
  Thermal = '../../../assets/images/DroneData/Thermal.png';
  isShown1 = 'visibility_off';
  isShown2 = 'visibility_off';
  slope_layer: any;
  dtm_layer: any;
  zoom_level: number;
  subdefects_visibility = 'visibility_off';
  grading_visibility = 'visibility_off';
  defect_rectify_visibility = 'visibility_off';
  accepted: boolean;
  accepted1: boolean;
  accepted2: boolean;
  accepted3: boolean;
  accepted4: boolean;
  accepted5 = false;
  get_missions_flights_data: any = null;
  table_number: any;
  markers: any;
  marker_data: any[] = [];
  sidebarVisible = true;
  tablegendselect = false;
  popupContent: any = null;
  isModalOpen = false;
  isFullscreen = false;
  selectedImage: string = null;
  selectedImage2 = 'terrain';
  tab_name: any;
  cad_val = false;
  project_id: number;
  currentValue = 50;
  opacity_value: any;
  userArray_value: any;
  userArray_Distance: any;
  csv_path: any;
  project_id_summary: any;
  minimum_value: number;
  maximum_value: number;
  slope: any;
  datevalue: any;
  graph_slope_bar: any = {};
  latitudes: any = [];
  longitudes: any = [];
  opacityStatus: boolean = false;
  rangeInput: HTMLInputElement;
  controlElevation: any = null;
  isOpacity: boolean = false;
  check: boolean = false;
  elevation: any;
  current_table_no: any;
  onceCalled: boolean = false;
  valuecheck: boolean = false;
  currentMenu: string = null;
  isOpen = true;
  projectInfo: { [key: string]: string } = {};
  drawnItems: any = new L.FeatureGroup();
  drawControl: any;
  markerItems: any;
  drawtool: Drawtool = new DrawtoolC();
  iconOptions = {
    iconUrl: '../../assets/images/marker.svg',
    iconSize: [30, 30],
  };
  @ViewChild('toolSidebar') toolSidebar!: ElementRef;

  constructor(
    private http: HttpClient,
    private _http: HttpService,
    private userService: UserService,
    private apiConfigService: ApiConfigService,
    private toastr: ToastrService,
    public dialog: MatDialog,
    private renderer: Renderer2,
    private el: ElementRef
  ) {}

  public ngOnInit(): void {
    this.logo = this.userService.getSidebarLogo();
    localStorage.setItem('thermal', 'thermal');
    localStorage.setItem('cad', 'cad');
    localStorage.setItem('DTM', 'DTM');
    localStorage.setItem('slope', 'slope');
    localStorage.setItem('satellite', 'satellite');
    localStorage.setItem('rawImage', 'rawImage');

    this.updateStyleOnResize();
    this.apiConfigService.getProjectData().subscribe(
      data => {
        this.summ = data['data'];
        this.center = this.summ;
        this.date = localStorage.getItem('date');
        this.project_id = this.summ.id;
        this.project_name = this.summ.name.replace(/_/g, ' ').toUpperCase();
        localStorage.setItem('project_name', `${this.project_name}`);
        this.projectInfo['projectName'] = this.project_name;
        this.projectInfo['projectDate'] = this.date;
        this.center = this.summ.center.split(',');
        this.lat = this.center[0].trim();
        this.long = this.center[1].trim();
        this.zoom_level = parseInt(data['data']['zoom_level']);
        this.tab_name = localStorage.getItem('tab_name');
        data['data']['dates'] = Object.keys(data['data']['date_status']);
        const date_inv_status_key = Object.keys(data['data']['date_status']);
        const date_inv_status_value = Object.values(data['data']['date_status']);
        for (let k = 0; k < date_inv_status_key.length; k++) {
          if (date_inv_status_value[k] == 'completed') {
            this.Completed_date_array.push(date_inv_status_key[k]);
          }
        }
        const processed_data = data['data']['processed_data'][this.date];
        this.csv_path = processed_data['csv_path'];
        const summary_layers = processed_data['summary_layers'] as Record<
          string,
          {
            defect_type: string;
            kml: string;
            sub_group?: Record<string, { criticality: string; kml: string; color: string }>;
            color: string;
          }
        >;
        Object.values(summary_layers).forEach(item => {
          if (item.sub_group && Object.keys(item.sub_group).length > 0) {
            Object.values(item.sub_group).forEach(sub_group_item => {
              this.allDefects.push({
                name: sub_group_item.criticality,
                kml: sub_group_item.kml,
                color: sub_group_item.color,
              });
            });
          } else {
            this.allDefects.push({ name: item.defect_type, kml: item.kml, color: item.color });
          }
        });
        this.Project_data_thermo = Object.keys(processed_data['summary_layers']);
        this.Project_data_grad = [
          Object.keys(processed_data['grading_layers']),
          ...Object.keys(processed_data['topography_layers']),
        ];
        if (this.tab_name == 'topography') {
          this.cad_val = true;
        }
        this.ortho_file_location = processed_data['ortho_file_location'];
        this.kml_file_location = processed_data['kml_file_location'];
        this.thermal_location = processed_data['thermal_hotspot_location'];
        this.cad_file_location = processed_data['cad_file_location'];
        this.map_generate();
      },
      err => {
        this.toastr.error(err.message, 'Error Occured');
      }
    );
  }

  @HostListener('window:resize', ['$event'])
  onWindowResize(): void {
    this.updateStyleOnResize();
  }

  private updateStyleOnResize(): void {
    const screenWidth = window.innerWidth;
    const element = this.el.nativeElement.querySelector('.tool-sidebar');
    const element2 = this.el.nativeElement.querySelector('.exam2');
    const element3 = this.el.nativeElement.querySelector('.toggle-right');
    if (element) {
      if (screenWidth < 390) {
        this.renderer.setStyle(element, 'zIndex', '2000');
      } else {
        this.renderer.removeStyle(element, 'zIndex');
      }
    }
    if (element2) {
      if (screenWidth < 390) {
        this.renderer.setStyle(element2, 'marginRight', '-2px');
      } else {
        this.renderer.removeStyle(element2, 'marginRight');
      }
    }

    if (element3) {
      if (screenWidth < 390) {
        this.renderer.setStyle(element3, 'zIndex', '4000');
      } else {
        this.renderer.removeStyle(element3, 'zIndex');
      }
    }
  }

  updateCurrentTime(): string {
    const now = new Date();
    // Extract date components
    const day = String(now.getDate()).padStart(2, '0');
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const year = now.getFullYear();

    // Extract time components
    let hours = now.getHours();
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

    // Determine AM or PM and convert to 12-hour format
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12 || 12; // Convert 0 to 12 for midnight and noon

    // Return the formatted date-time string
    return `${day}-${month}-${year} ${String(hours).padStart(2, '0')}:${minutes}:${seconds} ${ampm}`;
  }

  async exportMap(): Promise<void> {
    try {
      this.toastr.warning(
        'Please wait while we are preparing for screenshot',
        'Capturing Screenshot..'
      );
      this.currentTime = this.updateCurrentTime();

      const printMapContainer = document.querySelector('#print-map') as HTMLElement;
      printMapContainer.innerHTML = '';

      // Create new map container
      const printMapDiv = document.createElement('div');
      printMapDiv.className = 'print-map-content';
      printMapDiv.style.width = '100%';
      printMapDiv.style.height = '100%';
      printMapContainer.appendChild(printMapDiv);

      // Clone the map element
      const clonedMap = this.mapContainer.nativeElement.cloneNode(true) as HTMLElement;
      printMapDiv.appendChild(clonedMap);
      await new Promise(resolve => setTimeout(resolve, 2000));

      const mapInstance = (this.mapContainer.nativeElement as any).mapObject;
      if (mapInstance && mapInstance.invalidateSize) {
        mapInstance.invalidateSize();
      }
      window.print();
      printMapContainer.innerHTML = '';
    } catch (error) {
      console.error('Error capturing screenshot:', error);
      this.toastr.error('Failed to capture screenshot', 'Error!!');
    }
  }

  toggleLeftSidebar() {
    this.isOpen = !this.isOpen;
  }

  toggleRightSidebar() {
    this.sidebarVisible = !this.sidebarVisible;
  }

  openOpacity() {
    this.opacityStatus = !this.opacityStatus;
  }

  handleRangeChange(event: Event) {
    const rangeValue = (event.target as HTMLInputElement).value;
    this.opacity_value = parseFloat(rangeValue);
    if (this.selectedImage == 'thermal') this.thermal_layer.setOpacity(this.opacity_value);
    if (this.selectedImage == 'DTM') this.dtm_layer.setOpacity(this.opacity_value);
    if (this.selectedImage == 'slope') this.slope_layer.setOpacity(this.opacity_value);
  }

  onDrag({ movementX, movementY }: { movementX: number; movementY: number }, containerClass): void {
    const wrapper = document.querySelector(`.${containerClass}`) as HTMLElement;
    const getStyle = window.getComputedStyle(wrapper);
    wrapper.style.left = `${parseInt(getStyle.left) + movementX}px`;
    wrapper.style.top = `${parseInt(getStyle.top) + movementY}px`;
  }

  handleMouseDown(event: MouseEvent, containerClass: string): void {
    const wrapper = document.querySelector(`.${containerClass}`) as HTMLElement;
    const handleMouseMove = (event: MouseEvent): void => {
      this.onDrag({ movementX: event.movementX, movementY: event.movementY }, containerClass);
    };
    wrapper.addEventListener('mousemove', handleMouseMove);
    wrapper.classList.add('active');
    const handleMouseUp = (): void => {
      wrapper.classList.remove('active');
      wrapper.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
    document.addEventListener('mouseup', handleMouseUp);
  }

  createDataForGpxfile(table_no: string) {
    this.userArray_value = [];
    this.userArray_Distance = [];
    this.table_number = table_no;
    this.latitudes = [];
    this.longitudes = [];
    this.grading_visibility = 'visible';
    const headers = new HttpHeaders().set('Range', 'bytes=0-').set('X-Skip-Authorization', 'true');
    this.http.get(this.csv_path + table_no + '.csv', { headers, responseType: 'text' }).subscribe(
      data => {
        const csvToRowArray = data.split('\n');
        for (let index = 1; index < csvToRowArray.length - 1; index++) {
          const row = csvToRowArray[index].split(',');
          this.userArray_value.push(parseFloat(parseFloat(row[3].split('\r')[0]).toFixed(3)));
          this.userArray_Distance.push(parseFloat(parseFloat(row[0]).toFixed(3)));
          this.latitudes.push(parseFloat(row[2].split('\r')[0]));
          this.longitudes.push(parseFloat(row[1].split('\r')[0]));
        }
        this.minimum_value = Math.min(...this.userArray_value);
        this.maximum_value = Math.max(...this.userArray_value);
        const lineStringCoordinates = [];
        for (let i = 0; i < this.longitudes.length; i++) {
          const coordinate = [this.longitudes[i], this.latitudes[i], this.userArray_value[i]];

          lineStringCoordinates.push(coordinate);
        }
        const geoJSONLineString = {
          type: 'Feature',
          geometry: {
            type: 'LineString',
            coordinates: lineStringCoordinates,
          },
          properties: {
            name: 'MyLineString',
          },
        };
        const geoJSONFeatureCollection = {
          type: 'FeatureCollection',
          features: [geoJSONLineString],
        };
        const blob = new Blob([JSON.stringify(geoJSONFeatureCollection)], {
          type: 'application/json',
        });
        const blobUrl = URL.createObjectURL(blob);
        this.elevation = blobUrl;
        const opts = {
          elevationControl: {
            options: {
              position: 'bottomright',
              theme: 'lime-theme',
              detached: false,
              collapsed: false,
              autohide: false,
              legend: true,
              downloadLink: false,
              almostOver: true,
              distanceMarkers: { distance: false, direction: true, lazy: true },
            },
          },
          layersControl: {
            options: {
              position: 'bottomleft',
              collapsed: false,
            },
          },
        };
        if (!this.check)
          this.controlElevation = L.control
            .elevation(opts.elevationControl.options)
            .addTo(this.map);
        this.cleardata();
        this.load(this.elevation);
      },
      err => {
        this.toastr.error(err.message, 'Error Occured');
      }
    );
  }

  load(id) {
    this.controlElevation.load(id);
    this.check = true;
  }

  cleardata() {
    if (this.check) {
      this.controlElevation.clear();
    }
  }

  openModal(isModalOpen: boolean) {
    if (!isModalOpen) this.isModalOpen = true;
    else this.isModalOpen = false;
  }

  openWeather() {
    this.weather_show = !this.weather_show;
  }

  closeStack() {
    if (this.isModalOpen) this.isModalOpen = false;
  }

  toggleLegendBox() {
    this.tablegendselect = !this.tablegendselect;
  }

  toggleSidebarVisibility() {
    this.sidebarVisible = !this.sidebarVisible;
  }

  outerfunction(date: any) {
    localStorage.setItem('date', date);
    this._http.setNewUserInfo({
      dateval: date,
    });
    this._http.setclosesidebar({
      close_side_bar: 'summarySidebar/True',
    });
  }

  map_generate() {
    const openstreetmap = L.tileLayer(this.terrain_tile_layer, {
      minZoom: 2,
      maxZoom: 23,
      edgeBufferTiles: 1,
    });
    this.base_ortho_layer = L.tileLayer(this.ortho_file_location + '{z}/{x}/{y}.png', {
      minZoom: 2,
      maxZoom: 23,
      edgeBufferTiles: 1,
    });
    this.map = L.map(this.mapContainer.nativeElement, {
      preferCanvas: true,
      attributionControl: false,
      zoomControl: false,
      minZoom: 2,
      maxZoom: 23,
      zoomSnap: 0.5,
      twoFingerZoom: true,
      layers: [openstreetmap, this.base_ortho_layer],
      maxBoundsViscosity: 1.0,
      maxBounds: [
        [-90, -180],
        [90, 180],
      ],
    }).setView([0, 0], 2);

    L.control.scale().addTo(this.map);

    // new L.HistoryControl({
    //   backText: '<',
    //   forwardText: '>',
    // }).addTo(this.map);

    this.zoomreset();
    this.drawAOIPlugin();
    this.popup_card_visibility = false;
    this.popup_card_visibility_cadestral = false;
  }

  Datemenu(date) {
    this.date = date;
    const summ_data = this.summ['processed_data'][date];
    localStorage.setItem('date', date);
    this.storedDateValue = true;
    localStorage.setItem('report_path', JSON.stringify(summ_data['report_path']));
    this.Project_data = Object.keys(summ_data['summary_layers']);
    this.Project_data_values = summ_data['summary_layers'];
    this.Project_data_inverter = Object.keys(summ_data['inverter_layers']);
    this.Project_data_inverter_values = summ_data['inverter_layers'];
    this.ortho_file_location = summ_data['ortho_file_location'];
    this.kml_file_location = summ_data['kml_file_location'];
    this.thermal_location = summ_data['thermal_hotspot_location'];
    this.cad_file_location = summ_data['cad_file_location'];
    this.map.removeLayer(this.base_ortho_layer);

    this.base_ortho_layer = L.tileLayer(this.ortho_file_location + '{z}/{x}/{y}.png', {
      center: [this.center],
      minZoom: 2,
      maxZoom: 23,
    }).addTo(this.map);
    // added reload function on changing date
    window.location.reload();
  }

  zoomreset() {
    this.map.setView([this.lat, this.long], this.zoom_level);
  }

  zoomin() {
    this.map.setZoom(this.map.getZoom() + 1);
  }

  zoomout() {
    this.map.setZoom(this.map.getZoom() - 1);
  }

  toggleFullscreen() {
    this.isFullscreen = !this.isFullscreen;
    if (!document.fullscreenElement) {
      const docElm = document.documentElement as HTMLElement;
      if (docElm.requestFullscreen) {
        docElm.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  }

  isImage(value: any): boolean {
    return value.match(/\.(jpeg|jpg|png)$/) !== null;
  }

  loadPopUpContent_grading(table_no) {
    this.grading_visibility = 'visible';
    this._http.setgradingtable({
      gradingval: table_no,
    });
  }

  RemoveKml(value: any) {
    this.remove_Popup_card();
    this.accepted = false;
    this.accepted1 = false;
    this.subdefects_visibility = 'visiblity_off';
    this.grading_visibility = 'visiblity_off';
    this.defect_rectify_visibility = 'visibility_off';
    for (const n in this.removekml_list) {
      this.map.removeLayer(this.removekml_list[n]);
    }
    for (const n in this.removekml_list_inv) {
      this.map.removeLayer(this.removekml_list_inv[n]);
    }
    for (const l in this.polies) {
      this.map.removeLayer(this.polies[l]);
    }
    if (value != 'invertor')
      if (this.gb_layer != null) {
        this.map.removeLayer(this.gb_layer);
      }
  }

  polygonMarkerCreating(lat, col, dec) {
    const polygonPoints = [];
    for (let i = 0; i < lat.length; i += 3) {
      polygonPoints.push([lat[i + 1], lat[i], lat[i + 2]]);
    }
    this.poly = L.polygon(polygonPoints, { color: col, weight: 6 }).addTo(this.map);
    this.polies.push(this.poly);
    this.poly.on('click', e => {
      console.log('clicked');
      this.isModalOpen = false;
      const markup = new DOMParser().parseFromString(dec, 'text/html');
      const rows = Array.from(markup.getElementsByTagName('tr'));
      this.descObj = {};
      this.descObj_cadestral = {};
      const storageObj = ['cadastral_map', 'Vegetation'].includes(this.current_kml_data)
        ? this.descObj_cadestral
        : this.descObj;

      if (rows.length > 0) {
        rows.forEach(row => {
          const cells = row.getElementsByTagName('td');
          if (cells.length === 2) {
            const key = cells[0].innerText.trim().replace(':', '');
            const valTd = cells[1];
            const imgTag = valTd.querySelector('img');
            const value = imgTag ? imgTag.src : valTd.innerText.trim();
            if (key === 'Table No') {
              this.table_no = value;
            }
            storageObj[key] = value;
          }
        });
      } else {
        this.descObj_cadestral['Description'] = dec;
      }
      delete this.descObj['RGB_Image'];
      delete this.descObj['Thermal_Image'];
      switch (this.current_kml_data) {
        case 'cadastral_map':
        case 'Vegetation':
        case 'cadastral':
          this.popup_opened = true;
          L.popup({ closePopupOnClick: true, autoClose: true })
            .setLatLng([e.latlng.lat, e.latlng.lng])
            .setContent(dec)
            .openOn(this.map);
          break;
        case 'Grading':
          this.createDataForGpxfile(this.table_no);
          break;
        default:
          console.log('defect polygon visible');

          this.popup_card_visibility = true;
          this.popup_card_visibility_cadestral = false;
          if (this.descObj) this.popupContent = this.descObj;
      }
    });
  }

  remove_Popup_card() {
    if (this.popup_opened) {
      this.map.closePopup();
      this.popup_opened = false;
    }
  }

  updateValue(newValue: number): void {
    this.currentValue = newValue;
  }

  LoadKml(value) {
    this.accepted5 = false;
    this.tablegendselect = false;
    this.currentMenu = value.menu === 'inverter_sub_details' ? 'invertor' : value.menu;
    this.RemoveKml(this.currentMenu);
    this.current_kml_data = value.menu;
    this.summaryState = value.tab;

    localStorage.setItem('kmlfilename', this.summaryState);
    localStorage.setItem('mode', value.menu);
    localStorage.setItem('page', value.pageno);

    if (['cadastral', 'cadastral_map', 'Grading', 'Vegetation'].includes(value.menu)) {
      if (this.gb_layer) this.map.removeLayer(this.gb_layer);

      let layerData;
      if (value.menu === 'cadastral' || value.menu === 'cadastral_map') {
        layerData = this.summ['processed_data'][this.date]['topography_layers'];
        layerData =
          layerData['TopographicFeatures']?.['sub_feature'] ||
          layerData['Topography']?.['sub_feature'];
      } else if (value.menu === 'Grading') {
        layerData =
          this.summ['processed_data'][this.date]['grading_layers']['GradingFeatures'][
            'sub_feature'
          ];
      } else if (value.menu === 'Vegetation') {
        layerData =
          this.summ['processed_data'][this.date]['vegetation_layers']['Vegetation Features'][
            'sub_feature'
          ];
      }

      if (!layerData) return console.error('Sub-feature not found in JSON data');

      const kmlPaths = layerData[value.tab]?.kml?.split(',');
      this.subdefects_page_load('visibilty_off');

      if (kmlPaths?.length) {
        kmlPaths.forEach(kmlFile => {
          const kmlUrl = `${this.kml_file_location}GLOBAL/${kmlFile}.kml`;

          fetch(kmlUrl)
            .then(res => res.text())
            .then(kmltext => {
              const parser = new DOMParser();
              const kml = parser.parseFromString(kmltext, 'text/xml');
              this.track = new L.KML(kml);
              this.processKmlPlacemarks(kml, value.color);
              this.map.fitBounds(this.track.getBounds());
            });
        });
      }
    }

    if (['summary', 'summary_sub_details'].includes(value.menu)) {
      if (this.gb_layer) this.map.removeLayer(this.gb_layer);
      this.subdefects_page_load('visible');
      this.load_defects(`${this.kml_file_location}GLOBAL/`, this.summaryState.split(','));
    }

    if (['invertor', 'inverter_sub_details'].includes(value.menu)) {
      this.subdefects_page_load('visible');
      const inverterPath = `INVERTER${value.pageno.toString().padStart(2, '0')}`;
      this.load_defects(`${this.kml_file_location}${inverterPath}/`, this.summaryState.split(','));
    }

    this.close_popup_card();
  }

  processKmlPlacemarks(kml, color) {
    const placeMarks = kml.getElementsByTagName('Placemark');

    [...placeMarks].forEach(place => {
      const description = place.childNodes[1]?.textContent;
      const coordinates = place
        .getElementsByTagName('coordinates')[0]
        ?.textContent.replace(/\n/g, ' ')
        .split(/[ ,]+/)
        .filter(Boolean);

      const parser = new DOMParser();
      const parsedDesc = parser.parseFromString(description, 'text/html');
      const tableRows = parsedDesc.getElementsByTagName('td');

      let table_no = '';
      for (let i = 0; i < tableRows.length; i += 2) {
        if (tableRows[i].textContent === 'Defect:') {
          table_no = tableRows[i + 1]?.textContent;
          break;
        }
      }

      this.polygonMarkerCreating(
        coordinates,
        table_no === 'Hotspot' ? 'green' : color,
        description
      );
    });
  }

  defaultchart(dec) {
    const parser = new DOMParser();
    const markup = parser.parseFromString(dec, 'text/html');
    const y = markup.getElementsByTagName('td');
    this.table_no = y[1].textContent;
    this.createDataForGpxfile(this.table_no);
  }

  async load_defects(url, defectsArr) {
    for (const defect of defectsArr) {
      const response = await fetch(`${url}${defect}.kml`);
      const kmlText = await response.text();
      const parser = new DOMParser();
      const kml = parser.parseFromString(kmlText, 'text/xml');
      const placemarks = Array.from(kml.getElementsByTagName('Placemark'));
      for (const placemark of placemarks) {
        const description = placemark.childNodes[1].textContent;
        const coordinates = placemark
          .getElementsByTagName('coordinates')[0]
          .textContent.replace(/\n/g, ' ')
          .split(/[ ,]+/)
          .filter(Boolean);
        const descriptionParser = new DOMParser();
        const descriptionDocument = descriptionParser.parseFromString(description, 'text/html');
        const tdElements = descriptionDocument.getElementsByTagName('td');
        let tableNo = '';
        for (let i = 0; i < tdElements.length; i++) {
          if (i % 2 === 0) {
            tableNo = tdElements[i].textContent;
          } else if (tableNo === 'Defect:') {
            this.table_no = tdElements[i].textContent;
          }
        }
        const standardizedTableNo = this.table_no.trim().replace(/\s/g, '_').toLowerCase();
        const tableEntry = this.allDefects.find(
          entry => entry.name.trim().replace(/\s/g, '_').toLowerCase() === standardizedTableNo
        );
        if (tableEntry) {
          this.polygonMarkerCreating(coordinates, tableEntry.color, description);
        }
      }
      this.popupKml = kml;
    }
    localStorage.setItem('kml_popup_node', '');
  }

  subdefects_page_load(visibility: any) {
    this.subdefects_visibility = visibility;
    if (visibility == 'visible') {
      this._http.setsubdefects({
        status: visibility,
      });
    }
  }

  LoadGBKml(value: any) {
    if (this.gb_layer !== null) {
      this.map.removeLayer(this.gb_layer);
    }
    this.remove_Popup_card();
    let url;
    if (value <= 9 && value >= 1) {
      url = this.kml_file_location + 'INVERTER0' + value + '/gb.kml';
    } else {
      url = this.kml_file_location + 'INVERTER' + value + '/gb.kml';
    }
    fetch(url)
      .then(res => res.text())
      .then(kmltext => {
        const parser = new DOMParser();
        const kml = parser.parseFromString(kmltext, 'text/xml');
        this.gb_layer = new L.KML(kml);
        this.map.addLayer(this.gb_layer);
        this.popupKml = kml;
        const bounds = this.gb_layer.getBounds();
        this.map.fitBounds(bounds);
      });
    this.close_popup_card();
  }

  ViewMenu(option) {
    this.tablegendselect = false;
    this.selectedImage = option;
    this.isOpacity = ['thermal', 'DTM', 'slope'].includes(option);

    const layerMappings = {
      thermal: { key: 'thermal', location: this.thermal_location, layerVar: 'thermal_layer' },
      cad: { key: 'cad', location: this.cad_file_location, layerVar: 'cad_layer' },
      DTM: { key: 'DTM', location: this.cad_file_location, layerVar: 'dtm_layer' },
      slope: { key: 'slope', location: this.thermal_location, layerVar: 'slope_layer' },
    };

    if (layerMappings[option]) {
      const { key, location, layerVar } = layerMappings[option];
      this.toggleLayer(key, location, layerVar);
      return;
    }

    if (option === 'AllAnomalies') {
      this.toggleKML();
    }
  }

  toggleLayer(key, location, layerVar) {
    if (!location || location === 'None') {
      alert(`There is no ${key.toUpperCase()} for this project`);
      return;
    }

    const currentVal = localStorage.getItem(key);
    if (currentVal === key) {
      localStorage.setItem(key, 'none');
      this[layerVar] = L.tileLayer(`${location}{z}/{x}/{y}.png`, {
        center: [this.center],
        minZoom: 2,
        maxZoom: 23,
        opacity: this.opacity_value,
      }).addTo(this.map);
      this[`accepted${key}`] = true;
    } else {
      localStorage.setItem(key, key);
      this.map.removeLayer(this[layerVar]);
      this.selectedImage = 'none';
      this.isOpacity = false;
      this[`accepted${key}`] = false;
    }
  }

  toggleKML() {
    this.RemoveKml('allanomalies');
    this.accepted5 = !this.accepted5;
    this.selectedImage = this.accepted5 ? 'AllAnomalies' : 'none';

    if (this.accepted5) {
      this.allDefects.forEach(defect => {
        if (defect.kml !== 'NA') {
          this.fetchKML(defect.kml, defect.color);
        }
      });
    }
  }

  fetchKML(kmlFile, color) {
    fetch(`${this.kml_file_location}GLOBAL/${kmlFile}.kml`)
      .then(res => res.text())
      .then(kmltext => {
        const parser = new DOMParser();
        const kml = parser.parseFromString(kmltext, 'text/xml');
        this.track = new L.KML(kml);
        this.parseKML(kml, color);
        this.map.fitBounds(this.track.getBounds());
      });
  }

  parseKML(kml, color) {
    const placeMarks = kml.getElementsByTagName('Placemark');
    for (let i = 0; i < placeMarks.length; i++) {
      const description = placeMarks[i].childNodes[1]?.textContent;
      const coordinates = placeMarks[i].getElementsByTagName('coordinates')[0]?.textContent;

      if (description && coordinates) {
        const latlngArray = coordinates.replace(/\n/g, ' ').split(/[ ,]+/).filter(Boolean);
        this.polygonMarkerCreating(latlngArray, color, description);
      }
    }
  }

  map_view(image: string) {
    this.selectedImage2 = image;
    if (image == 'satellite') {
      this.satellite_layer = L.tileLayer(this.sattelite_tile_layer, {
        center: [this.lat, this.long],
        minZoom: 2,
        maxZoom: 23,
      }).addTo(this.map);
      L.tileLayer(this.ortho_file_location + '{z}/{x}/{y}.png', {
        center: [this.lat, this.long],
        minZoom: 2,
        maxZoom: 23,
      }).addTo(this.map);
    } else {
      if (this.satellite_layer) {
        L.tileLayer(this.terrain_tile_layer, {
          minZoom: 2,
          maxZoom: 23,
        }).addTo(this.map);
        L.tileLayer(this.ortho_file_location + '{z}/{x}/{y}.png', {
          center: [this.lat, this.long],
          minZoom: 2,
          maxZoom: 23,
        }).addTo(this.map);
      }
    }
  }

  close_popup_card() {
    this.popup_card_visibility = false;
    this.popup_card_visibility_cadestral = false;
    this.popup_card_visibility_grading = false;
  }

  subdefects_page(latlong) {
    this.map.setView(new L.LatLng(latlong[0], latlong[1]), 22);
    const marker = L.marker([latlong[0], latlong[1]], {
      icon: L.icon(this.iconOptions),
    }).addTo(this.map);
    const lat = latlong[0];
    const lon = latlong[1];
    marker.bindTooltip(`Latitude: ${lat}<br>Longitude: ${lon}`).openTooltip();
    setTimeout(() => {
      this.map.removeLayer(marker);
    }, 4000);
  }

  rectification_defect(defect) {
    this.defect_rectify_visibility = 'visible';
    defect;
    // const dialogRef = this.dialog.open(DefectrectificationComponent);
    // dialogRef.afterClosed().subscribe(result => {});
  }

  getUserLocation() {
    if ('geolocation' in navigator) {
      navigator.geolocation.getCurrentPosition(
        position => {
          const userLat = position.coords.latitude;
          const userLng = position.coords.longitude;
          const userMarker = L.marker([userLat, userLng], {
            icon: L.icon(this.iconOptions),
          }).addTo(this.map);
          userMarker.bindPopup('Your Current Location').openPopup();
          this.map.flyTo([userLat, userLng], 18);
        },
        error => {
          this.toastr.error(error.message, 'Error Occurred');
        },
        {
          enableHighAccuracy: true,
          timeout: 5000,
          maximumAge: 0,
        }
      );
    } else {
      this.toastr.error('Geolocation is not available in this browser.', 'Error Occurred');
    }
  }

  drawAOIPlugin(): void {
    this.drawnItems = new L.FeatureGroup().addTo(this.map);
    this.markerItems = new L.FeatureGroup().addTo(this.map);
    const MyCustomMarker = L.Icon.extend({ options: this.iconOptions });
    this.drawControl = new L.Control.Draw({
      position: 'topright',
      draw: {
        polyline: true,
        polygon: { showArea: true, showLength: true },
        circle: { showArea: true, showLength: true },
        rectangle: { showArea: false, showLength: true },
        marker: { icon: new MyCustomMarker() },
        circlemarker: false,
      },
      edit: {
        featureGroup: this.drawnItems,
        remove: false,
      },
    });
    const getLayerType = (layer: any): string => {
      if (layer instanceof L.Polygon) return 'polygon';
      if (layer instanceof L.Rectangle) return 'rectangle';
      if (layer instanceof L.Circle) return 'circle';
      if (layer instanceof L.Polyline) return 'polyline';
      if (layer instanceof L.Marker) return 'marker';
      return 'unknown';
    };

    this.map.on(L.Draw.Event.CREATED, (event: any) => {
      this.handleLayerCreated(event.layerType, event.layer, 'created');
    });

    this.map.on(L.Draw.Event.EDITED, (event: any) => {
      event.layers.eachLayer((layer: any) => {
        const layerType = getLayerType(layer);
        if (layerType !== 'unknown') {
          this.handleLayerCreated(layerType, layer, 'edited');
        } else {
          console.error('Unhandled layer type in edited event:', layer);
        }
      });
    });
  }

  handleLayerCreated(layerType: string, layer: any, action: string): void {
    let coordinates: any;
    let area: number;

    const calculateArea = (coords: any): number => L.GeometryUtil.geodesicArea(coords);

    const calculatePolylineDistance = (coords: any): number =>
      coords.reduce((total, curr, idx, arr) => {
        if (idx === 0) return 0;
        return total + arr[idx - 1].distanceTo(curr);
      }, 0);

    switch (layerType) {
      case 'polygon':
      case 'rectangle':
        coordinates = layer.getLatLngs()[0];
        area = calculateArea(coordinates);
        break;
      case 'circle':
        coordinates = layer.getLatLng();
        area = Math.PI * Math.pow(layer.getRadius(), 2);
        break;
      case 'polyline':
        coordinates = layer.getLatLngs();
        area = calculatePolylineDistance(coordinates);
        break;
      case 'marker':
        coordinates = layer.getLatLng();
        area = 0;
        break;
      default:
        console.error('Unknown layer type:', layerType);
        return;
    }
    Object.assign(this.drawtool, {
      area: Number(area.toFixed(2)),
      aoi_type: layerType,
      polygon: coordinates,
      label: '',
      description: '',
    });

    const dialogRef = this.dialog.open(AoiDialogComponent, {
      data: { drawtool: this.drawtool },
    });
    dialogRef.afterClosed().subscribe((result: any) => {
      if (result?.drawtool) {
        Object.assign(this.drawtool, {
          label: result.drawtool.label,
          description: result.drawtool.description,
        });
        if (action == 'created') {
          this.drawtool.id = this.project_id;
          this.drawtool.date = this.date;
          this.sidebarComponent.createDrawtool(this.drawtool);
        } else if (action == 'edited') {
          this.sidebarComponent.updateDrawtool(this.drawtool);
        }
        this.map.removeControl(this.drawControl);
      }
    });
  }

  updateAOIView(event: { action: string; data: any }) {
    const { action, data } = event;
    this.markerItems?.clearLayers();
    if (action !== 'edit') {
      this.drawnItems?.clearLayers();
    } else {
      this.drawtool = data;
    }
    const manageDrawControl = (shouldAdd: boolean) => {
      if (this.drawControl) {
        shouldAdd
          ? this.map.addControl(this.drawControl)
          : this.map.removeControl(this.drawControl);
      }
    };
    switch (action) {
      case 'add':
      case 'edit':
        manageDrawControl(true);
        break;
      case 'remove':
        manageDrawControl(false);
        break;
      case 'show':
        manageDrawControl(false);
        this.renderAOI(data);
        break;
      default:
        console.error(`Unhandled action: ${action}`, event);
    }
  }

  renderAOI(aoi) {
    const addMarkers = (coordinates: any, label: string): void => {
      const markers = L.layerGroup();
      coordinates.forEach(coord => {
        const popupContent = `
        <b>Label</b>: ${label}<br/>
        <b>Latitude</b>: ${coord.lat.toFixed(6)}<br/>
        <b>Longitude</b>: ${coord.lng.toFixed(6)}<br/>
      `;
        const marker = L.marker(coord, {
          icon: L.icon(this.iconOptions),
        });
        marker.bindPopup(popupContent).openPopup();
        markers.addLayer(marker);
      });
      this.markerItems.addLayer(markers);
    };

    if (aoi.aoi_type === 'polygon' || aoi.aoi_type === 'rectangle' || aoi.aoi_type === 'polyline') {
      const polygon = L.polygon(aoi.polygon, {
        color: 'darkcyan',
        weight: 1,
      });
      addMarkers(polygon.getLatLngs()[0], aoi.label);
      this.drawnItems.addLayer(polygon);
      this.map.fitBounds(polygon.getBounds());
    } else if (aoi.aoi_type === 'circle') {
      const circleCenter = L.latLng(aoi.polygon.lat, aoi.polygon.lng);
      const circle = L.circle(circleCenter, {
        color: 'darkcyan',
        radius: Math.sqrt(aoi.area / Math.PI),
      });
      addMarkers([circleCenter], aoi.label);
      this.drawnItems.addLayer(circle);
      this.map.setView(circleCenter, 18);
    } else if (aoi.aoi_type === 'marker') {
      const marker = L.marker(aoi.polygon, {
        icon: L.icon(this.iconOptions),
      });
      const popupContent = `
        <b>Label</b>: ${aoi.label}<br/>
        <b>Latitude</b>: ${aoi.polygon.lat.toFixed(6)}<br/>
        <b>Longitude</b>: ${aoi.polygon.lng.toFixed(6)}<br/>
      `;
      marker.bindPopup(popupContent).openPopup();
      this.drawnItems.addLayer(marker);
      this.map.setView(aoi.polygon, 18);
    }
  }
}
