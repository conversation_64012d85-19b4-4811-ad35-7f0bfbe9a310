{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"carnot": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "css"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-builders/custom-webpack:browser", "options": {"customWebpackConfig": {"path": "./extra-webpack.config.js"}, "outputPath": "dist/carnot", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "aot": true, "buildOptimizer": true, "assets": [{"glob": "**/*", "input": "./node_modules/leaflet/dist/images", "output": "leaflet/"}, "src/favicon.ico", "src/assets/"], "styles": ["node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/ngx-toastr/toastr.css", "node_modules/leaflet-draw/dist/leaflet.draw-src.css", "node_modules/leaflet/dist/leaflet.css", "node_modules/@optometristpritam/leaflet-height/leaflet-elevation/dist/leaflet-elevation.css", "src/assets/css/style.min.css", "src/styles.scss"], "scripts": ["node_modules/bootstrap/dist/js/bootstrap.min.js", "node_modules/jquery/dist/jquery.min.js", "node_modules/leaflet-side-by-side/index.js", "node_modules/leaflet-draw/dist/leaflet.draw-src.js", "src/assets/plugins/leaflet.edgebuffer.js", "src/assets/plugins/leaflet.history.js", "src/assets/plugins/leaflet.twofingerzoom.js"], "allowedCommonJsDependencies": ["j<PERSON>y", "leaflet", "leaflet-side-by-side", "plotly.js-dist-min", "base64-js", "js-sha256", "crypto-js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "7mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "15kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": false, "extractLicenses": false, "sourceMap": true, "namedChunks": false}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "carnot:build:production", "proxyConfig": "src/proxy.conf.json"}, "development": {"buildTarget": "carnot:build:development", "proxyConfig": "src/proxy.conf.json"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "carnot:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/font-awesome/css/font-awesome.min.css", "src/styles.scss"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/popper.js/dist/umd/popper.min.js", "node_modules/bootstrap/dist/js/bootstrap.min.js"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "carnot:serve"}, "configurations": {"production": {"devServerTarget": "carnot:serve:production"}}}}}}, "cli": {"analytics": false}}