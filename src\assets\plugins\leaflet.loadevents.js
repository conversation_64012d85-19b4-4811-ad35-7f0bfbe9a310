!function(){var i=L.FeatureGroup,t=i.prototype;function e(i){i._id2ctx||(i._id2ctx={},i._id2ldg={})}function d(){var i=this.lg,t=this.id,e=i._id2ldg;if(!(t in e)){var d=!r(e);e[t]=!0,d&&i.fire("loading")}}function n(){var i=this.lg,t=this.id,e=i._id2ldg;t in e&&(delete e[t],r(e)||i.fire("load"))}function r(i){for(var t in i)if(i.hasOwnProperty(t))return!0;return!1}i.LoadEvents=i.extend({includes:L.Mixin.Events,addLayer:function(i){e(this);var r=this.getLayerId(i);if(!(r in this._id2ctx)){delete this._id2ldg[r];var a=this._id2ctx[r]={lg:this,id:r};i.on({loading:d,load:n},a)}return t.addLayer.apply(this,arguments)},removeLayer:function(i){e(this);var r=this.getLayerId(i);if(r in this._id2ctx){var a=this._id2ctx[r];i.off({loading:d,load:n},a),delete this._id2ctx[r],delete this._id2ldg[r]}return t.removeLayer.apply(this.arguments)}}),i.loadEvents=function(t){return new i.LoadEvents(t)}}();