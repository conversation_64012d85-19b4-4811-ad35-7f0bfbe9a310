<mat-toolbar>
  <!-- <span  fxShow="false">
    <button mat-icon-button (click)="openSidebar()">
      <mat-icon>menu</mat-icon>
    </button>
  </span> -->
  <!-- <span class="menu-section" fxShow="true">
    <button mat-mini-fab [matMenuTriggerFor]="menu">
      <mat-icon>perm_identity</mat-icon>
    </button>
    <mat-menu #menu="matMenu">
      <button mat-menu-item [routerLink]="['/app', 'my-profile']">
        <mat-icon>account_circle</mat-icon>
        My Profile
      </button>
      <button mat-menu-item [routerLink]="['/app', 'manage-users']" *appHasRole="'admin'">
        <mat-icon>supervised_user_circle</mat-icon>
        Manage users
      </button>
      <button mat-menu-item (click)="logout()">
        <mat-icon>logout</mat-icon>
        Logout
      </button>
    </mat-menu>
  </span> -->
</mat-toolbar>
