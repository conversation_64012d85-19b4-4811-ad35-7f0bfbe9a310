<div class="card container mt-4">
  <mat-tab-group [selectedIndex]="2">
    <mat-tab
      label="{{ category }}"
      *ngFor="let category of categorywise_project"
      [disabled]="category != 'thermography'">
      <div class="card-body" *ngIf="category == 'thermography'">
        <div class="row">
          <div class="box-margin col-lg-6">
            <plotly-plot
              [data]="graph.data"
              [layout]="graph.layout"
              [config]="graph.config"></plotly-plot>
          </div>
          <div class="box-margin col-lg-6">
            <plotly-plot
              [data]="graph1.data"
              [layout]="graph1.layout"
              [config]="graph1.config"></plotly-plot>
          </div>
        </div>
        <div class="row">
          <div class="box-margin col-md-12 justify-content-center align-items-center">
            <plotly-plot
              [data]="graph2.data"
              [layout]="graph2.layout"
              [config]="graph2.config"></plotly-plot>
          </div>
        </div>
      </div>
      <div class="card-body p-0" *ngIf="category != 'thermography'">
        <div class="cm">Coming soon....</div>
      </div>
    </mat-tab>
  </mat-tab-group>
</div>
