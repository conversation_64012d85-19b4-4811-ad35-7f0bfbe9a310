<div class="exam2">
  <mat-select
    name="date"
    [(ngModel)]="datevalue"
    (ngModelChange)="onUserDateChange()"
    matTooltip="Change Date">
    <mat-option [value]="date" *ngFor="let date of Completed_date_array">
      {{ date }}
    </mat-option>
  </mat-select>
</div>
<div class="tab-wrapper">
  <mat-tab-group (selectedTabChange)="tabClick($event)">
    <mat-tab label="Power Loss Metrics">
      <ng-container *ngIf="powerloss_style === 'hidden'; else showPowerLoss">
        <p class="no-project">There is no data for the given date</p>
      </ng-container>
      <ng-template #showPowerLoss>
        <mat-card class="anal-graphs">
          <mat-card-content>
            <div class="dash-cont">
              <div *ngFor="let stat of statsData" class="rowcard2">
                <div class="sub-dashcont">
                  <h5 class="analytics-cont">
                    <b>{{ stat.title }}</b>
                  </h5>
                  <h2 class="metric-value">{{ stat.value }} {{ stat.unit }}</h2>
                  <span class="metric-sub">{{ stat.subtitle }}</span>
                </div>
                <button
                  mat-fab
                  aria-label="Stat Icon"
                  style="background-color: var(--primary) !important">
                  <mat-icon>{{ stat.icon }}</mat-icon>
                </button>
              </div>
            </div>
            <div class="graph-card">
              <div class="statistic">
                <div id="summary_im" class="banner"></div>
                <div class="p-2">
                  <div class="title">Defect Details</div>
                  <div *ngIf="summary_def_def" class="details">
                    <div class="key">Defect Type</div>
                    <div class="value" id="summary_def">{{ summary_def_def }}</div>
                  </div>
                  <div *ngIf="summary_def_cou" class="details">
                    <div class="key">Estimated Power Loss (kW)</div>
                    <div class="value" id="summary_cou">{{ summary_def_cou }}</div>
                  </div>
                </div>
              </div>

              <div class="graph-container">
                <plotly-plot
                  [data]="graph_power_pie.data"
                  [layout]="graph_power_pie.layout"
                  [config]="graph_power_pie.config"
                  (plotlyClick)="onClickPowerPie($event)">
                </plotly-plot>
              </div>

              <div class="graph-container">
                <plotly-plot
                  [data]="graph_power_donut_pie.data"
                  [layout]="graph_power_donut_pie.layout"
                  [config]="graph_power_donut_pie.config">
                </plotly-plot>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </ng-template>
    </mat-tab>
    <mat-tab label="Anomaly Classification">
      <ng-container *ngIf="anamolies_style === 'hidden'; else showAnomaly">
        <p class="no-project">There is no data for the given date</p>
      </ng-container>
      <ng-template #showAnomaly>
        <mat-card class="anal-graphs">
          <mat-card-content>
            <div class="graph-card">
              <div class="statistic">
                <div id="ana_summary_im" class="banner"></div>
                <div class="p-2">
                  <div class="title">Defect Details</div>
                  <div class="details" *ngIf="ana_def_def">
                    <div class="key">Defect Type</div>
                    <div class="value" id="ana_summary_def">{{ ana_def_def }}</div>
                  </div>
                  <div class="details">
                    <div class="key">Defect Count</div>
                    <div class="value" id="ana_summary_cou">{{ ana_def_cou }}</div>
                  </div>
                </div>
              </div>

              <div class="graph-container">
                <plotly-plot
                  [data]="graph_anamoly_bar.data"
                  [layout]="graph_anamoly_bar.layout"
                  [config]="graph_anamoly_bar.config"
                  (plotlyClick)="onClickanamolyBar($event)">
                </plotly-plot>
              </div>
            </div>
            <div class="graph-card">
              <div class="statistic">
                <div id="ana_summary_im_1" class="banner"></div>
                <div class="p-2">
                  <div class="title">Defect Details</div>
                  <div class="details">
                    <div class="key">Defect Type</div>
                    <div class="value" id="ana_summary_def_1">{{ ana_def_def }}</div>
                  </div>
                  <div class="details">
                    <div class="key">Defect Count</div>
                    <div class="value" id="ana_summary_cou_1">{{ ana_def_cou }}</div>
                  </div>
                  <div class="details">
                    <div class="key">Percentage</div>
                    <div class="value" id="ana_summary_cou_perc_1">{{ ana_def_cou_perc_1 }}</div>
                  </div>
                  <div class="details">
                    <div class="key">Description</div>
                    <div class="value" id="ana_summary_cou_desc_1">{{ ana_def_cou_desc_1 }}</div>
                  </div>
                </div>
              </div>

              <div class="graph-container">
                <plotly-plot
                  [data]="graph_anamoly_pie.data"
                  [layout]="graph_anamoly_pie.layout"
                  [config]="graph_anamoly_pie.config"
                  (plotlyClick)="onClickanamolyPie($event)">
                </plotly-plot>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </ng-template>
    </mat-tab>
    <mat-tab label="Inverter-Wise Analysis">
      <ng-container *ngIf="inverter_style === 'hidden'; else showInverter">
        <p class="no-project">There is no data for the given date</p>
      </ng-container>
      <ng-template #showInverter>
        <mat-card class="anal-graphs">
          <mat-card-content>
            <div class="graph-card">
              <div class="statistic">
                <div class="p-2">
                  <div class="title text-center">
                    <b>Total Defect Count Details</b>
                  </div>
                  <ng-container *ngFor="let detail of inverterCount">
                    <div class="details" *ngIf="detail.value">
                      <div class="key">{{ detail.key }}</div>
                      <div class="value countValue">{{ detail.value }}</div>
                    </div>
                  </ng-container>
                  <p>
                    <span class="font-weight-bold">Note: </span>
                    Click on the bar graph to switch between the inverters.
                  </p>
                </div>
              </div>
              <div class="graph-container">
                <plotly-plot
                  [data]="graph_inverter_bar_2.data"
                  [layout]="graph_inverter_bar_2.layout"
                  [config]="graph_inverter_bar_2.config"
                  (plotlyClick)="onClickInverterBar2($event)">
                </plotly-plot>
              </div>
            </div>
            <div class="graph-card">
              <div class="statistic">
                <div class="p-2">
                  <div class="title text-center">
                    <b>Defect Details</b>
                  </div>
                  <ng-container *ngFor="let detail of defectDetails">
                    <div class="details" *ngIf="detail.value">
                      <div class="key">{{ detail.key }}</div>
                      <div class="value" [id]="sanitizeClassName(detail.key)">
                        {{ detail.value }}
                      </div>
                    </div>
                  </ng-container>
                  <p>
                    <span class="font-weight-bold">Note:</span>
                    Click on the bar graph to switch between the inverters.
                  </p>
                </div>
              </div>
              <div class="graph-container">
                <plotly-plot
                  [data]="graph_inverter_bar.data"
                  [layout]="graph_inverter_bar.layout"
                  [config]="graph_inverter_bar.config"
                  (plotlyClick)="onClick($event)">
                </plotly-plot>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </ng-template>
    </mat-tab>
    <mat-tab label="Health History">
      <ng-container *ngIf="health_style === 'hidden'; else showHealth">
        <p class="no-project">There is no data for the given date</p>
      </ng-container>
      <ng-template #showHealth>
        <mat-card class="anal-graphs">
          <mat-card-content>
            <div class="graph-card">
              <plotly-plot
                [data]="graph_health_bar.data"
                [layout]="graph_health_bar.layout"
                [config]="graph_health_bar.config">
              </plotly-plot>
            </div>
            <div class="graph-card">
              <plotly-plot
                [data]="graph_health_pie.data"
                [layout]="graph_health_pie.layout"
                [config]="graph_health_pie.config">
              </plotly-plot>
            </div>
          </mat-card-content>
        </mat-card>
      </ng-template>
    </mat-tab>
    <mat-tab label="Losses">
      <ng-container *ngIf="power_loss_values_energy !== ''; else showLossEmpty">
        <mat-card class="anal-graphs">
          <mat-card-content>
            <div class="losses-container">
              <div class="loss-panel">
                <div class="analytics-card-title">
                  <h3 class="page-title">Energy Loss</h3>
                </div>
                <div class="input-group">
                  <label class="m-0">Specific prod</label>
                  <div class="input-control">
                    <input
                      type="text"
                      #energyInput="ngModel"
                      [(ngModel)]="energyValue"
                      name="energyValue" />
                    <span>kWh/kWp/year</span>
                  </div>
                </div>
                <div class="input-group">
                  <label class="m-0">Total power loss in kW as calculated</label>
                  <div class="input-control">
                    <span>{{ total_power_loss_datewise }} kW</span>
                  </div>
                </div>
                <div class="input-group">
                  <button
                    mat-button
                    (click)="energyloss_calc()"
                    [disabled]="!energyValue || +energyValue <= 0">
                    Calculate
                  </button>
                </div>
                <div *ngIf="Chart_hide_show == 'show'" class="result-card">
                  <div class="result-label">Total energy loss is</div>
                  <div class="result-value">{{ total_energy_loss }} kWh/year</div>
                </div>
                <table class="data-table mt-3">
                  <thead>
                    <tr>
                      <th>Energy Loss Category wise</th>
                      <th></th>
                      <th></th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let i of power_loss_keys; let d = index">
                      <td>{{ power_loss_keys[d] }}</td>
                      <td class="value-cell">{{ energy_defectwise[d] }}</td>
                      <td class="unit-cell">kWh/kWp/year</td>
                    </tr>
                  </tbody>
                </table>
                <div *ngIf="Chart_hide_show == 'show'">
                  <div class="graph-panel">
                    <plotly-plot
                      [data]="graph_losses_pie.data"
                      [layout]="graph_losses_pie.layout"
                      [config]="graph_losses_pie.config">
                    </plotly-plot>
                  </div>
                </div>
              </div>
              <div class="loss-panel">
                <div class="analytics-card-title">
                  <h3 class="page-title">Revenue Loss</h3>
                </div>
                <div class="input-group">
                  <label class="m-0">Tariff</label>
                  <div class="input-control">
                    <input
                      type="text"
                      #revenueInput="ngModel"
                      [(ngModel)]="revenueValue"
                      name="revenueValue" />
                    <span>INR/kWh</span>
                  </div>
                </div>
                <div class="input-group mt-4">
                  <button
                    mat-button
                    (click)="tariff_cal()"
                    [disabled]="!revenueValue || +revenueValue <= 0">
                    Calculate
                  </button>
                </div>
                <div *ngIf="Chart_hide_show_revenue == 'show'" class="result-card">
                  <div class="result-label">Revenue Loss is</div>
                  <div class="result-value">{{ total_tariff }} INR / Year</div>
                </div>
                <table class="data-table mt-3">
                  <thead>
                    <tr>
                      <th>Revenue Loss Category wise</th>
                      <th></th>
                      <th></th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let i of power_loss_keys; let y = index">
                      <td>{{ power_loss_keys[y] }}</td>
                      <td class="value-cell">{{ revenue_defectwise[y] }}</td>
                      <td class="unit-cell">INR / Year</td>
                    </tr>
                  </tbody>
                </table>
                <div *ngIf="Chart_hide_show_revenue == 'show'">
                  <div class="graph-panel">
                    <plotly-plot
                      [data]="graph_losses_pie_2.data"
                      [layout]="graph_losses_pie_2.layout"
                      [config]="graph_losses_pie_2.config">
                    </plotly-plot>
                  </div>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </ng-container>
      <ng-template #showLossEmpty>
        <div class="no-data-message">
          <p>There is no data for the given date</p>
        </div>
      </ng-template>
    </mat-tab>
    <mat-tab label="Parts per million">
      <ng-container *ngIf="power_loss_values_revenue !== ''; else showPpmEmpty">
        <mat-card class="anal-graphs">
          <mat-card-content>
            <div class="ppm-container">
              <div class="analytics-card-title">
                <h3 class="page-title">Parts per million (PPM) - Losses</h3>
              </div>
              <div class="ppm-layout">
                <div class="ppm-panel">
                  <div class="panel-title">For Power loss</div>
                  <table class="ppm-table">
                    <tr>
                      <td>DC Capacity in kW</td>
                      <td>{{ DC_Capacity_in_KW }}</td>
                      <td>kW</td>
                    </tr>
                    <tr>
                      <td>Power loss in kW</td>
                      <td>{{ total_power_loss_datewise }}</td>
                      <td>kW</td>
                    </tr>
                    <tr>
                      <td>PPM</td>
                      <td>{{ Total_power_loss_in_ppm }}</td>
                      <td>PPM(kWH)</td>
                    </tr>
                  </table>
                </div>
                <div class="ppm-panel">
                  <div class="panel-title">For Energy loss</div>
                  <table class="ppm-table">
                    <tr>
                      <td>Total Energy</td>
                      <td>{{ total_energy_loss_in_ppm }}</td>
                      <td>kWh</td>
                    </tr>
                    <tr>
                      <td>Energy loss in kW</td>
                      <td>{{ total_energy_loss_in_KW_ppm }}</td>
                      <td>kWh</td>
                    </tr>
                    <tr>
                      <td>PPM</td>
                      <td>{{ total_energy_loss_calc_in_ppm }}</td>
                      <td>PPM (kWh)</td>
                    </tr>
                  </table>
                </div>
                <div class="ppm-panel">
                  <div class="panel-title">For Revenue loss</div>
                  <table class="ppm-table">
                    <tr>
                      <td>Total Expected Revenue</td>
                      <td>{{ total_revenue_loss_in_ppm }}</td>
                      <td>INR</td>
                    </tr>
                    <tr>
                      <td>Predicted Revenue loss</td>
                      <td>{{ total_revenue_loss_in_KW_ppm }}</td>
                      <td>INR</td>
                    </tr>
                    <tr>
                      <td>PPM</td>
                      <td>{{ total_revenue_loss_in_ppm_INR }}</td>
                      <td>PPM (INR)</td>
                    </tr>
                  </table>
                </div>
              </div>
              <div class="analytics-card-title">
                <h3 class="page-title">Parts per million (PPM) - Defects (DPPM)</h3>
              </div>
              <div class="ppm-chart-container">
                <div class="ppm-data">
                  <table class="ppm-table">
                    <tr *ngFor="let i of health_history_key_data; let k = index">
                      <td>{{ health_history_key_data[k] }}</td>
                      <td>{{ module_count_defectwise_value[k] }}</td>
                      <td>PPM</td>
                    </tr>
                  </table>
                </div>
                <div class="ppm-chart">
                  <plotly-plot
                    [data]="graph_ppm_bar.data"
                    [layout]="graph_ppm_bar.layout"
                    [config]="graph_ppm_bar.config">
                  </plotly-plot>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </ng-container>
      <ng-template #showPpmEmpty>
        <div class="no-data-message">
          <p>There is no data for the given date</p>
        </div>
      </ng-template>
    </mat-tab>
  </mat-tab-group>
</div>
