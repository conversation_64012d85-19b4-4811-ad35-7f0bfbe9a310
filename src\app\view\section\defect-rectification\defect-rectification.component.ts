import { ApiConfigService } from '@/controller/api-config.service';
import { PaginationRequest, PaginationResponse } from '@/interfaces/pagination';
import { HttpService } from '@/view/map-section/services-map/http.service';
import { DatePipe } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';
import { ToastrService } from 'ngx-toastr';
import { merge, Observable, Subject } from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  map,
  startWith,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs/operators';

@Component({
  changeDetection: ChangeDetectionStrategy.OnPush,
  selector: 'app-defect-rectification',
  templateUrl: './defect-rectification.component.html',
  styleUrls: ['./defect-rectification.component.css'],
  providers: [DatePipe],
})
export class DefectRectificationComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;
  dataSource = new MatTableDataSource<any>();
  totalItems = 0;
  currentPage = 0;
  pageSize = 50;
  pageSizeOptions = [50, 100, 150, 200];
  sortField = '';
  sortDirection = '';
  isDataLoading = true;
  destroy$ = new Subject<void>();
  filterChange$ = new Subject<any>();
  isUploading = false;
  searchControl = new FormControl('');
  layoutFile: string = null;
  selectedDefect: string = '';
  selectedBlock: string = '';
  selectedTable: string = '';
  globalFilter: string = '';
  selectedSeverity: string = '';
  selectedVersion: string = '';
  projectInfo: any = {};
  defectTypes: { type: string }[] = [];
  blockNumbers: { block: string }[] = [];
  tableTypes: { table: string }[] = [];
  severityTypes: { severity: string }[] = [];
  versionTypes: { version: string }[] = [];
  filteredDefects: { type: string }[] = [];
  filteredBlocks: { block: string }[] = [];
  filteredTables: { table: string }[] = [];
  filteredSeverity: { severity: string }[] = [];
  filteredVersions: { version: string }[] = [];
  allColumns: string[] = [
    'Defect',
    'Block No.',
    'Table No.',
    'Module No.',
    'Severity',
    'Delta(Δ) Temp.(°C)',
    'Status',
    'Rectified By',
    'Remarks',
    'Timestamp',
    'Days Taken',
  ];
  displayedColumns: string[] = [...this.allColumns];
  private responseCache = new Map<string, PaginationResponse>();
  private deltaCache = new Map<string, string>();
  private daysCache = new Map<string, number>();
  private timestampCache = new Map<string, string>();

  constructor(
    private _http: HttpService,
    private toastr: ToastrService,
    private apiConfigService: ApiConfigService,
    private datePipe: DatePipe,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this._http.set_analytics_icon(true);
    this.projectInfo['project_id'] = localStorage.getItem('project_id');
    this.projectInfo['project_name'] = localStorage.getItem('project_name');
    this.projectInfo['date'] = localStorage.getItem('date');
    this.layoutFile =
      JSON.parse(localStorage.getItem('report_path'))?.['LAYOUT'] ?? this.layoutFile;
    this.searchControl.valueChanges
      .pipe(takeUntil(this.destroy$), debounceTime(500), distinctUntilChanged())
      .subscribe(value => {
        this.globalFilter = value || '';
        this.applyFilter();
      });
  }
  ngAfterViewInit(): void {
    setTimeout(() => {
      this.setupServerSidePagination();
    }, 100);
  }
  ngOnDestroy(): void {
    this._http.set_analytics_icon(false);
    this.destroy$.next();
    this.destroy$.complete();
  }
  setupServerSidePagination(): void {
    if (!this.paginator || !this.sort) return;
    merge(
      this.sort.sortChange.pipe(tap(() => (this.paginator.pageIndex = 0))),
      this.paginator.page,
      this.filterChange$
    )
      .pipe(
        startWith({}),
        takeUntil(this.destroy$),
        switchMap(() => {
          this.isDataLoading = true;
          const request: PaginationRequest = {
            page: this.paginator.pageIndex,
            pageSize: this.paginator.pageSize,
            sortField: this.sort.active,
            sortDirection: this.sort.direction,
            filters: {
              globalFilter: this.globalFilter ? this.globalFilter.trim().toLowerCase() : '',
              defect: this.selectedDefect ? this.selectedDefect.trim().toLowerCase() : '',
              block: this.selectedBlock ? this.selectedBlock.trim().toLowerCase() : '',
              table: this.selectedTable ? this.selectedTable.trim().toLowerCase() : '',
              severity: this.selectedSeverity ? this.selectedSeverity.toLowerCase() : '',
              version: this.selectedVersion ? this.selectedVersion.toString().toLowerCase() : '',
            },
          };

          return this.loadPaginatedData(request);
        })
      )
      .subscribe({
        next: (response: PaginationResponse) => {
          this.isDataLoading = false;
          this.totalItems = response.totalCount;
          this.dataSource.data = response.items;
          if (
            response.items.length > 0 &&
            !this.globalFilter &&
            !this.selectedDefect &&
            !this.selectedBlock &&
            !this.selectedTable &&
            !this.selectedSeverity &&
            !this.selectedVersion
          ) {
            this.updateFilters(response.items);
          }
          this.cdr.detectChanges();
        },
        error: error => {
          this.isDataLoading = false;
          console.error('Error loading data:', error);
          this.toastr.warning('Error loading data. Please try again.');
        },
      });
  }
  loadPaginatedData(request: PaginationRequest): Observable<PaginationResponse> {
    const { project_id, date } = this.projectInfo;
    const cacheKey = this.createCacheKey(project_id, date, request);
    const hasActiveFilters =
      !!request.filters.globalFilter ||
      !!request.filters.defect ||
      !!request.filters.block ||
      !!request.filters.table ||
      !!request.filters.severity ||
      !!request.filters.version;

    const cachedResponse = !hasActiveFilters ? this.responseCache.get(cacheKey) : null;
    if (cachedResponse) {
      return new Observable(observer => {
        observer.next(cachedResponse);
        observer.complete();
      });
    }
    return this.apiConfigService.getDefectData(project_id, date, request).pipe(
      map((response: any) => {
        const results = response.data?.results || [];
        const pagination = response.data?.pagination || { total: 0 };
        const transformedItems = this.transformData(results);
        const mappedItems = transformedItems.map(item => ({
          ...item,
          version: item.version + 1,
        }));
        const paginatedResponse = {
          items: mappedItems,
          totalCount: pagination.total,
        };
        if (!hasActiveFilters) {
          this.responseCache.set(cacheKey, paginatedResponse);
          if (this.responseCache.size > 20) {
            const firstKey = this.responseCache.keys().next().value;
            this.responseCache.delete(firstKey);
          }
        }
        return paginatedResponse;
      })
    );
  }
  private createCacheKey(projectId: string, date: string, request: PaginationRequest): string {
    const filters = request.filters || {};
    const sortedFilters = Object.keys(filters)
      .sort()
      .reduce((obj, key) => {
        obj[key] = filters[key];
        return obj;
      }, {});

    return `${projectId}_${date}_${request.page}_${request.pageSize}_${request.sortField || ''}_${request.sortDirection || ''}_${JSON.stringify(sortedFilters)}`;
  }
  transformData(data: any[]): any[] {
    if (!data || !Array.isArray(data)) {
      return [];
    }
    const result: any[] = [];
    for (const item of data) {
      if (!item) continue;
      try {
        let version0 = item;
        if (Array.isArray(item.previous_versions) && item.previous_versions.length > 0) {
          for (const prev of item.previous_versions) {
            if (prev && prev.version === 0) {
              version0 = prev;
              break;
            }
          }
        }
        const latestItem = {
          ...this.mapItemToTableFormat(item, version0),
          version: item.version,
          is_latest: true,
          id: `${item.block_number}_${item.table_number}_${item.module_number}_${item.version}`,
        };
        result.push(latestItem);
        if (
          this.selectedVersion &&
          Array.isArray(item.previous_versions) &&
          item.previous_versions.length > 0
        ) {
          for (const prev of item.previous_versions) {
            if (!prev) continue;
            const prevItem = {
              ...this.mapItemToTableFormat(prev, version0),
              version: prev.version,
              is_latest: false,
              id: `${prev.block_number}_${prev.table_number}_${prev.module_number}_${prev.version}`,
            };
            const versionForComparison = (prev.version + 1).toString();
            if (this.selectedVersion === versionForComparison || this.selectedVersion === 'all') {
              result.push(prevItem);
            }
          }
        }
      } catch (error) {
        console.error('Error processing item:', error);
      }
    }
    return result;
  }
  private getDaysTaken(
    item: any,
    _version0: any,
    cacheKey: string,
    createdAt: string,
    updatedAt: string
  ): number {
    if (item.version === 0) {
      return 0;
    }
    const daysTaken = this.daysCache.get(cacheKey);
    if (daysTaken !== undefined) {
      return daysTaken;
    }
    if (createdAt && updatedAt) {
      const result = this.calculateDaysTakenUtil(createdAt, updatedAt);
      this.daysCache.set(cacheKey, result);
      return result;
    }
    return 0;
  }
  private getTemperatureValues(item: any): { minTemp: number | null; maxTemp: number | null } {
    const minTemp = item.minimum_temp ?? null;
    const maxTemp = item.maximum_temp ?? null;
    return { minTemp, maxTemp };
  }
  mapItemToTableFormat(item: any, version0: any): any {
    if (!item) {
      return {};
    }
    if (!version0) {
      version0 = item;
    }
    const { minTemp, maxTemp } = this.getTemperatureValues(item);
    const updatedAt = item.updated_at || '';
    const createdAt = version0.created_at || '';
    const itemId = `${item.block_number || ''}_${item.table_number || ''}_${item.module_number || ''}_${item.version}`;
    const deltaKey = `${minTemp}_${maxTemp}_${itemId}`;
    const daysKey = `${createdAt}_${updatedAt}_${itemId}`;
    let deltaTemp = this.deltaCache.get(deltaKey);
    if (deltaTemp === undefined) {
      deltaTemp = this.calculateDeltaTemp(minTemp, maxTemp);
      this.deltaCache.set(deltaKey, deltaTemp);
    }
    const daysTaken = this.getDaysTaken(item, version0, daysKey, createdAt, updatedAt);
    let timestamp = this.timestampCache.get(updatedAt);
    if (timestamp === undefined && updatedAt) {
      timestamp = this.datePipe.transform(updatedAt, 'dd-MM-yyyy, hh:mm:ss a') || '';
      this.timestampCache.set(updatedAt, timestamp);
    }
    return {
      Defect: item.defect_type || '',
      'Block No.': item.block_number || '',
      'Table No.': item.table_number || '',
      'Module No.': item.module_number || '',
      Severity: item.severity || '',
      'Delta(Δ) Temp.(°C)': deltaTemp,
      Status: item.status || '',
      'Rectified By': item.rectified_by || '',
      Remarks: item.remarks || '',
      Timestamp: timestamp,
      'Days Taken': daysTaken,
      version: item.version !== undefined ? item.version : 0,
      is_latest: !!item.is_latest,
    };
  }
  onSelectionChange(event: any): void {
    this.displayedColumns = event.value;
  }
  calculateDeltaTemp(minTemp: number | string, maxTemp: number | string): string {
    const minTempNum = typeof minTemp === 'string' ? parseFloat(minTemp) : minTemp;
    const maxTempNum = typeof maxTemp === 'string' ? parseFloat(maxTemp) : maxTemp;
    if (minTempNum == null || maxTempNum == null || isNaN(minTempNum) || isNaN(maxTempNum)) {
      return '-';
    }
    try {
      const avgTemp = (minTempNum + maxTempNum) / 2;
      const delta = maxTempNum - avgTemp;
      return delta.toFixed(2);
    } catch {
      return '-';
    }
  }
  calculateDaysTakenUtil(startDate: string, endDate: string): number {
    if (!startDate || !endDate) {
      return 0;
    }

    try {
      const start = new Date(startDate);
      const end = new Date(endDate);
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return 0;
      }
      const diffTime = end.getTime() - start.getTime();
      return Math.max(0, Math.floor(diffTime / (1000 * 60 * 60 * 24)));
    } catch {
      return 0;
    }
  }
  updateFilters(data: any[]): void {
    if (!data || !Array.isArray(data) || data.length === 0) {
      this.defectTypes = [];
      this.blockNumbers = [];
      this.tableTypes = [];
      this.severityTypes = [];
      this.versionTypes = [];
      this.filteredDefects = [];
      this.filteredBlocks = [];
      this.filteredTables = [];
      this.filteredSeverity = [];
      this.filteredVersions = [];
      this.cdr.detectChanges();
      return;
    }

    try {
      const defects = [...new Set(data.map((item: any) => item && item['Defect']).filter(Boolean))];
      const blocks = [
        ...new Set(data.map((item: any) => item && item['Block No.']).filter(Boolean)),
      ];
      const tables = [
        ...new Set(data.map((item: any) => item && item['Table No.']).filter(Boolean)),
      ];
      const severities = [
        ...new Set(data.map((item: any) => item && item['Severity']).filter(Boolean)),
      ];
      const versions = [
        ...new Set(data.map((item: any) => item && item['version']).filter(Boolean)),
      ];
      defects.sort();
      blocks.sort();
      tables.sort();
      severities.sort();
      versions.sort((a, b) => Number(a) - Number(b));
      this.defectTypes = defects.map(type => ({ type }));
      this.blockNumbers = blocks.map(block => ({ block }));
      this.tableTypes = tables.map(table => ({ table }));
      this.severityTypes = severities.map(severity => ({ severity }));
      this.versionTypes = versions.map(version => ({ version }));
      this.filteredDefects = [...this.defectTypes];
      this.filteredBlocks = [...this.blockNumbers];
      this.filteredTables = [...this.tableTypes];
      this.filteredSeverity = [...this.severityTypes];
      this.filteredVersions = [...this.versionTypes];
    } catch (error) {
      // Silently handle error and continue
    }
    this.cdr.detectChanges();
  }
  resetFilters(): void {
    this.displayedColumns = [...this.allColumns];
    this.globalFilter = '';
    this.searchControl.setValue('', { emitEvent: false });

    this.selectedDefect = '';
    this.selectedBlock = '';
    this.selectedTable = '';
    this.selectedSeverity = '';
    this.selectedVersion = '';

    this.filteredDefects = [...this.defectTypes];
    this.filteredBlocks = [...this.blockNumbers];
    this.filteredTables = [...this.tableTypes];
    this.filteredSeverity = [...this.severityTypes];
    this.filteredVersions = [...this.versionTypes];

    this.clearCaches();

    if (this.paginator) this.paginator.firstPage();
    setTimeout(() => {
      this.filterChange$.next(Date.now());
      this.cdr.detectChanges();
      this.toastr.info('Filters reset!!');
    }, 0);
  }

  clearFilter(filterName: string): void {
    switch (filterName) {
      case 'defect':
        this.selectedDefect = '';
        break;
      case 'block':
        this.selectedBlock = '';
        break;
      case 'table':
        this.selectedTable = '';
        break;
      case 'severity':
        this.selectedSeverity = '';
        break;
      case 'version':
        this.selectedVersion = '';
        break;
    }
    this.applyFilter();
  }

  private clearCaches(): void {
    this.responseCache.clear();
    this.deltaCache.clear();
    this.daysCache.clear();
    this.timestampCache.clear();
  }
  applyFilter() {
    this.clearCaches();
    if (this.paginator && this.paginator.pageIndex !== 0) {
      this.paginator.pageIndex = 0;
    }

    this.isDataLoading = true;
    setTimeout(() => {
      this.filterChange$.next(Date.now());
      this.cdr.detectChanges();
    }, 0);
  }
  clearSearch(): void {
    this.searchControl.setValue('', { emitEvent: true });
    this.applyFilter();
  }

  onFilteredDefects(filteredData: Record<string, string>[]): void {
    this.filteredDefects = filteredData.map(item => ({
      type: item['type'] || '',
    }));
  }

  onFilteredBlocks(filteredData: Record<string, string>[]): void {
    this.filteredBlocks = filteredData.map(item => ({
      block: item['block'] || '',
    }));
  }

  onFilteredTables(filteredData: Record<string, string>[]): void {
    this.filteredTables = filteredData.map(item => ({
      table: item['table'] || '',
    }));
  }

  onFilteredSeverity(filteredData: Record<string, string>[]): void {
    this.filteredSeverity = filteredData.map(item => ({
      severity: item['severity'] || '',
    }));
  }

  onFilteredVersions(filteredData: Record<string, string>[]): void {
    this.filteredVersions = filteredData.map(item => ({
      version: item['version'] || '',
    }));
  }
  triggerFileInput(): void {
    const fileInput = document.querySelector('.file-input') as HTMLInputElement;
    fileInput?.click();
  }
  onFileChange(event: any): void {
    const file = event.target.files[0];
    if (file) {
      if (!file.name.endsWith('.xlsx')) {
        this.toastr.warning('Only .xlsx files are allowed.');
        return;
      }
      const reader = new FileReader();
      reader.onload = async (e: any) => {
        const arrayBuffer = e.target.result;
        const workbook = new ExcelJS.Workbook();
        try {
          await workbook.xlsx.load(arrayBuffer);
          const sheetName = workbook.worksheets[0].name;
          const worksheet = workbook.getWorksheet(sheetName);
          const headers = this.extractHeaders(worksheet).sort();
          const expectedHeaders = [
            'Defect Type',
            'Block No.',
            'Table No.',
            'Module No.',
            'Delta(Δ) Temp.(°C)',
            'Status',
            'Remarks',
          ].sort();
          if (!this.compareHeaders(headers, expectedHeaders)) {
            this.toastr.warning('The uploaded file does not have the required headers.');
            return;
          }
          this.extractDataFromWorksheet(worksheet);
          this.toastr.info('File validated successfully and ready for upload.');
          this.proceedWithUpload(file);
        } catch (error) {
          this.toastr.error('An error occurred while processing the file.');
        }
      };
      reader.readAsArrayBuffer(file);
    }
  }
  extractHeaders(worksheet: ExcelJS.Worksheet): string[] {
    const headers: string[] = [];
    const headerRow = worksheet.getRow(3);
    headerRow.eachCell(cell => {
      headers.push(cell.value ? cell.value.toString() : '');
    });
    return headers;
  }
  compareHeaders(headers: string[], expectedHeaders: string[]): boolean {
    return (
      headers.length === expectedHeaders.length &&
      expectedHeaders.every((header, index) => headers[index] === header)
    );
  }
  extractDataFromWorksheet(worksheet: ExcelJS.Worksheet): any[] {
    const data: any[] = [];
    worksheet.eachRow((row, rowIndex) => {
      if (rowIndex > 3) {
        const rowData: any = {};
        row.eachCell((cell, colNumber) => {
          const header = worksheet.getRow(3).getCell(colNumber).value;
          if (header) rowData[header.toString()] = cell.value ? cell.value.toString() : '';
        });
        data.push(rowData);
      }
    });
    return data;
  }
  proceedWithUpload(file: File): void {
    if (!file.name.endsWith('.xlsx')) {
      this.toastr.warning('Only .xlsx files are allowed.');
      return;
    }
    this.isUploading = true;
    const formData = new FormData();
    formData.append('file', file, file.name);
    formData.append('project_id', this.projectInfo['project_id']);
    formData.append('project_name', this.projectInfo['project_name']);
    formData.append('date', this.projectInfo['date']);

    this.apiConfigService.createDefectData(formData).subscribe({
      next: (res: any) => {
        if (res['status'] === 'success') {
          this.resetFilters();
          this.resetFileInput();
          this.toastr.success(res['message'], 'File uploaded successfully!');

          this.clearCaches();
          this.filterChange$.next(Date.now());
        } else {
          this.toastr.warning(res['message'] || 'Upload completed with warnings', 'Warning');
          if (res['errors']) {
            if (res['errors']['excel_parsing'] && res['errors']['excel_parsing'].length > 0) {
              this.toastr.error('Excel parsing errors detected', 'Validation Error');
            }
            if (
              res['errors']['defect_processing'] &&
              res['errors']['defect_processing'].length > 0
            ) {
              this.toastr.error('Defect processing errors detected', 'Processing Error');
            }
          }
        }
        this.isUploading = false;
      },
      error: (error: any) => {
        let errorMessage = 'Failed to upload file';

        if (error.error && error.error.message) {
          errorMessage = error.error.message;
        } else if (error.message) {
          errorMessage = error.message;
        }

        this.toastr.error(errorMessage, 'Upload Failed');
        this.isUploading = false;
        this.resetFileInput();
      },
      complete: () => {
        this.isUploading = false;
      },
    });
  }
  resetFileInput(): void {
    const fileInput = document.querySelector('.file-input') as HTMLInputElement;
    if (fileInput) fileInput.value = '';
  }
  async exportToExcel(): Promise<void> {
    if (this.dataSource.filteredData.length === 0) {
      this.toastr.warning('There is no data');
      return;
    }
    this.toastr.info('Please wait..', 'Exporting Started!!!');

    try {
      const filteredData = this.dataSource.filteredData.map(item => ({
        'Defect Type': item['Defect'] || '',
        'Block No.': item['Block No.'] || '',
        'Table No.': item['Table No.'] || '',
        'Module No.': item['Module No.'] || '',
        'Delta(Δ) Temp.(°C)': item['Delta(Δ) Temp.(°C)'] || '',
        Status: item['Status'] || 'pending',
        Remarks: item['Remarks'] || '',
      }));

      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Defect Rectification');

      worksheet.mergeCells('A1:G1');
      worksheet.mergeCells('A2:G2');

      const projectName = this.projectInfo['project_name'];
      const projectDate = this.projectInfo['date'];
      worksheet.getCell('A1').value = `Project Name: ${projectName}`;
      worksheet.getCell('A2').value = `Date: ${projectDate}`;

      [worksheet.getCell('A1'), worksheet.getCell('A2')].forEach(cell => {
        cell.font = { bold: true, size: 14 };
        cell.alignment = { horizontal: 'center', vertical: 'middle' };
      });

      const headers = Object.keys(filteredData[0]);
      const headerRow = worksheet.addRow(headers);

      headerRow.eachCell(cell => {
        cell.font = { bold: true };
        cell.alignment = { horizontal: 'center', vertical: 'middle' };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
        cell.protection = { locked: true };
      });

      filteredData.forEach(dataRow => {
        const row = worksheet.addRow(Object.values(dataRow));
        row.eachCell((cell, colNumber) => {
          const isEditable = colNumber === 6 || colNumber === 7;
          cell.protection = { locked: !isEditable };
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
        });
      });

      const statusOptions = ['done', 'pending', 'in-progress'];
      const firstDataRow = 4;

      for (let rowIndex = firstDataRow; rowIndex <= worksheet.rowCount; rowIndex++) {
        const statusCell = worksheet.getCell(rowIndex, 6);
        statusCell.dataValidation = {
          type: 'list',
          allowBlank: false,
          formulae: [`"${statusOptions.join(',')}"`],
          showErrorMessage: true,
          errorTitle: 'Invalid Selection',
          error: 'Please select a valid status from the dropdown.',
        };
      }
      worksheet.columns.forEach(column => {
        column.width = 18;
      });
      await worksheet.protect('datasee@india', {
        selectLockedCells: false,
        selectUnlockedCells: true,
      });
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      saveAs(blob, `${projectName}_${projectDate}_Defect-Rectification-Report.xlsx`);
    } catch (error) {
      this.toastr.error('Error exporting to Excel. Please try again.');
    }
  }
  async downloadLayoutFile() {
    await this._http.fileDownload(this.projectInfo['project_name'], this.layoutFile);
  }
}
