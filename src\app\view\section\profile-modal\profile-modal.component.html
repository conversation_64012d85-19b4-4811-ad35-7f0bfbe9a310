<div class="data">
  <div class="container-fluid mt-3">
    <form>
      <div class="row mb-2">
        <div class="col m-1">
          <label for="exampleInput1" class="form-label">Name:</label>
          <input
            type="text"
            class="form-control"
            id="exampleInput1"
            disabled
            name="name"
            [(ngModel)]="data.user.name" />
        </div>
        <div class="col m-1">
          <label for="exampleInput3" class="form-label">Email:</label>
          <input
            type="text"
            class="form-control"
            id="exampleInput3"
            disabled
            name="email"
            [(ngModel)]="data.user.email" />
        </div>
      </div>
      <div class="row mb-2">
        <div class="col m-1">
          <label for="exampleInput4" class="form-label">Created On:</label>
          <input
            type="text"
            class="form-control"
            id="exampleInput4"
            disabled
            name="created_on"
            [ngModel]="data.user.created_on | date: 'dd-MM-yyyy HH:mm:ss a'" />
        </div>
        <div class="col m-1">
          <label for="exampleInput5" class="form-label">Role:</label>
          <select class="form-select" id="exampleInput5" name="role" [(ngModel)]="data.user.role">
            <option [value]="null" disabled selected>Please select an option</option>
            <option *ngFor="let option of data.role" [value]="option">
              {{ option | uppercase }}
            </option>
          </select>
        </div>
      </div>
    </form>
    <mat-dialog-actions style="justify-content: space-evenly; margin-bottom: 0.5rem">
      <button mat-button class="w-25 text-uppercase" [disabled]="Isworking" (click)="onUpdate()">
        <span *ngIf="!Isworking">Submit</span>
        <span *ngIf="Isworking">
          <div class="spinner-border" role="status" *ngIf="Isworking"></div>
        </span>
      </button>
      <button mat-button (click)="onClose()" class="w-25 text-uppercase">Close</button>
    </mat-dialog-actions>
  </div>
</div>
