#weather {
  min-width: 300px;
  border: solid 2px darkcyan;
  transition: display 0.3s ease-in-out;
  padding: 5px;
  background-color: white;
  border-radius: 10px;
}

#header {
  text-align: left;
  font-size: 16px;
  padding: 5px;
  font-weight: 600;
  background-color: darkcyan;
  color: white;
  border-radius: 5px;
}

#header2 {
  display: flex;
  flex-direction: column;
  align-items: center;
}

#img-temp {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}

#temp-unit {
  font-size: 20px;
  position: absolute;
  padding-top: 10px;
  vertical-align: top;
}

#header4 {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}

#header4 #header4-part {
  display: flex;
  flex-direction: column;
  align-items: center;
  border-right: solid 1px gray;
  padding: 4px;
}

#header4 #header4-part .value1 {
  font-size: 13px;
  font-weight: bold;
}

.tooltipweather .tooltiptext {
  visibility: hidden;
  width: 153px;
  background-color: whitesmoke;
  border: solid 2px white;
  color: black;
  text-align: center;
  border-radius: 5px;
  padding: 5px 0;
  position: absolute;
  z-index: 1;
  bottom: 100%;
  left: 50%;
  margin-left: -100px;
  font-size: 14px;
  font-weight: 600;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}
