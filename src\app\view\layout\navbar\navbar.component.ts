import { Component } from '@angular/core';

@Component({
  selector: 'navbar',
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.css'],
})
export class NavbarComponent {
  // @Output() toggleSidebar: EventEmitter<true> = new EventEmitter();
  // lastname: any;
  // firstname: any;
  // isAdmin: any;
  // logo: string;
  // constructor(
  //   private sharedDataService: SharedDataService,
  // ) {}
  // ngOnInit(): void {
  //   this.firstname = this.sharedDataService.get().first_name;
  //   this.lastname = this.sharedDataService.get().last_name;
  //   this.isAdmin = this.AuthService.isAdmin();
  // }
  // logout() {
  //   this.sharedDataService.destroy();
  // }
  // openSidebar() {
  //   this.toggleSidebar.emit();
  // }
}
