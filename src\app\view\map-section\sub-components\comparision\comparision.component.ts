import { ApiConfigService } from '@/controller/api-config.service';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import 'leaflet';
import 'leaflet-kml';
import 'leaflet-side-by-side';
import { ToastrService } from 'ngx-toastr';
import { environment } from 'src/environments/environment';
declare const L: any;

@Component({
  selector: 'app-comparision',
  templateUrl: './comparision.component.html',
  styleUrls: ['./comparision.component.css'],
})
export class ComparisionComponent implements OnInit {
  terrain_tile_layer: string = environment.terrain_tile_layer;
  sattelite_tile_layer: string = environment.sattelite_tile_layer;
  map: any;
  main_data: any;
  base_ortho_layer: any;
  polygonLayers = { left: [], right: [] };

  defects_data_left = [];
  defects_data_right = [];
  kml_file_location_right: any;
  kml_file_location_left: any;
  thermal_hotspot_location_left: any;
  thermal_hotspot_location_right: any;
  ortho_file_location_left: any;
  ortho_file_location_right: any;
  cad_file_location_left: any;
  cad_file_location_right: any;
  cad_location_left_plane: any;
  cad_location_right_plane: any;

  dateArray = [];
  defects: any;
  optionleft: any;
  optionright: any;
  polies = [];
  descObj: any;
  popup_card_visibility: boolean;

  tileLayerUrl: string = '';
  defect_left: string = '';
  defect_right: string = '';
  dateleft: string;
  dateright: string;
  lat: string;
  long: string;
  rightLayerGroup = null;
  leftLayerGroup = null;
  allDefects = [];
  isModalOpen: boolean = false;
  isFullscreen: boolean = false;
  isAllDefects: boolean = false;
  selectedImage: string = null;
  selectedImage2: string = null;
  default = '../../../../../assets/images/default.jpg';
  satellite = '../../../../../assets/images/satellite.jpg';
  thermal = '../../../../../assets/images/DroneData/Thermal.png';
  cad = '../../../assets/images/DroneData/CAD.png';
  dtm = '../../../../assets/images/DroneData/DSM.png';
  Comparision = '../../../../assets/images/DroneData/compare.svg';
  ActualPlanned = '../../../../assets/images/DroneData/slide.svg';
  Temporal = '../../../../assets/images/DroneData/time.svg';
  legendleft = '';
  legendright = '';
  planOptions: string[] = ['Plan', 'Actual', 'Difference'];
  leftPlan: string;
  rightPlan: string;
  isdateleftright: boolean = true;
  leftPolygonToggle: boolean = true;
  rightPolygonToggle: boolean = true;
  popupData = {};
  legendColors = {};
  commonlegendUrl: string = '';
  legendtemp: string = '';
  legendshow = false;
  isDEM = false;
  planMapping: { [key: string]: string } = {
    Plan: 'DTM',
    Actual: 'DSM',
    Difference: 'DEM',
  };
  legendMapping: { [key: string]: string } = {
    Plan: 'DTM',
    Actual: 'DTM',
    Difference: 'DEM',
  };
  icons = [
    {
      imageSrc: this.Temporal,
      menuType: 'cad',
      title: 'Temporal Comparison: DSM Data',
      description: 'Shows differences in DSM data across selected time periods for trend analysis.',
      showTooltip: false,
    },
    {
      imageSrc: this.Comparision,
      menuType: 'comparison',
      title: 'Comprehensive Terrain and Elevation Analysis',
      description:
        'An interactive dashboard offering multiple perspectives on terrain and elevation changes.',
      showTooltip: false,
    },
    {
      imageSrc: this.ActualPlanned,
      menuType: 'dtm',
      title: 'Planned vs. Actual Comparison',
      description: 'Comparison between planned and actual metrics to grading',
      showTooltip: false,
    },
  ];
  zoom_option = { minZoom: 2, maxZoom: 23 };

  constructor(
    private router: Router,
    private toastr: ToastrService,
    private apiConfigService: ApiConfigService
  ) {}

  ngOnInit(): void {
    this.apiConfigService.getProjectData().subscribe(
      (data: any) => {
        this.main_data = data['data'];

        const { center, date_status: dateStatus } = this.main_data;
        const [lat, long] = center.split(',').map(Number);
        this.lat = lat;
        this.long = long;

        this.dateArray = Object.keys(dateStatus).filter(key => dateStatus[key] === 'completed');
        const firstDate = this.dateArray[0];
        if (this.dateArray.length >= 2) {
          this.dateleft = firstDate;
          this.dateright = this.dateArray[1];
        } else {
          this.dateleft = this.dateright = firstDate;
        }

        const processed_data_left = this.main_data['processed_data'][this.dateleft];
        const processed_data_right = this.main_data['processed_data'][this.dateright];

        this.ortho_file_location_left = processed_data_left['ortho_file_location'];
        this.ortho_file_location_right = processed_data_right['ortho_file_location'];

        this.cad_file_location_left = processed_data_left['cad_file_location'];
        this.cad_file_location_right = processed_data_right['cad_file_location'];

        this.kml_file_location_left = processed_data_left['kml_file_location'];
        this.kml_file_location_right = processed_data_right['kml_file_location'];

        this.thermal_hotspot_location_left = processed_data_left['thermal_hotspot_location'];
        this.thermal_hotspot_location_right = processed_data_right['thermal_hotspot_location'];

        this.defects_data_left = this.getData(processed_data_left);
        this.defects_data_right = this.getData(processed_data_right);

        this.allDefects = this.defects_data_left.map(defect => defect.key);

        const date = localStorage.getItem('date');
        const processedData = this.main_data?.['processed_data']?.[date]?.['report_path'];
        this.isDEM = processedData?.['DEM'];

        if (this.isDEM) {
          const report_path = processedData?.['DEM'];

          this.commonlegendUrl = report_path.legend;
          this.legendtemp = `${report_path.legend}DTM.png`;

          this.cad_location_left_plane = report_path.base_url;
          this.cad_location_right_plane = report_path.base_url;

          this.leftPlan = this.planOptions[0];
          this.rightPlan = this.planOptions[1];
          this.optionleft = this.planMapping['Plan'];
          this.optionright = this.planMapping['Actual'];
          this.legendleft = `${this.commonlegendUrl}${this.legendMapping[this.leftPlan]}.png`;
          this.legendright = `${this.commonlegendUrl}${this.legendMapping[this.rightPlan]}.png`;
        }
        this.generateMap();
      },
      (err: any) => {
        this.toastr.error(err.message, 'Error Occured');
      }
    );
  }
  getRandomColor() {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
  }
  toggleAllDefects() {
    this.isAllDefects = !this.isAllDefects;
    this.defect_left = '';
    this.defect_right = '';
    this.legendColors = {};

    const kmlKey = this.isAllDefects ? 'all' : '';
    this.kmlName(kmlKey, 'left');
    this.kmlName(kmlKey, 'right');
  }
  kmlName(kml_key: string, side: string): void {
    this.popup_card_visibility = false;
    this.isModalOpen = false;
    this.isAllDefects = kml_key === 'all';
    if (this.defect_left == '') this.clearPolygonLayer('left');
    if (this.defect_right == '') this.clearPolygonLayer('right');

    this.clearPolygonLayer(side);
    if (kml_key) {
      const defectsToProcess = kml_key === 'all' ? this.allDefects : [kml_key];
      if (side === 'left') {
        this.leftPolygonToggle = true;
      } else if (side === 'right') {
        this.rightPolygonToggle = true;
      }
      const defectsData = side === 'left' ? this.defects_data_left : this.defects_data_right;
      defectsToProcess.forEach(defectKey => {
        const defectData = defectsData.find(obj => obj.key === defectKey);
        if (defectData) {
          const kml = defectData.kml.split(',');
          const color = this.getRandomColor();
          if (!this.legendColors[defectKey]) {
            this.legendColors[defectKey] = { left: '', right: '' };
          }
          this.legendColors[defectKey][side] = color;
          const kmlFileLocation =
            side === 'left' ? this.kml_file_location_left : this.kml_file_location_right;
          this.loadDefects(kmlFileLocation, kml, color, side);
        }
      });
    }
  }
  async loadDefects(url: string, defectsArr: string[], color: string, side: string) {
    for (const defect of defectsArr) {
      try {
        const response = await fetch(`${url}GLOBAL/${defect}.kml`);
        if (!response.ok) {
          console.error('Failed to fetch the KML data');
          return;
        }
        const kmlText = await response.text();
        const kml = new DOMParser().parseFromString(kmlText, 'text/xml');
        const placemarks = Array.from(kml.getElementsByTagName('Placemark'));
        for (const placemark of placemarks) {
          const description = placemark.querySelector('description')?.textContent || '';
          const coordinatesText = placemark.querySelector('coordinates')?.textContent?.trim() || '';
          const coordinates = coordinatesText.split(/[ ,]+/).filter(Boolean).map(Number);
          this.createPolygonMarker(coordinates, color, description, side);
        }
      } catch (error) {
        console.error(`Error loading defect ${defect}:`, error);
      }
    }
  }
  togglePolygonLayer(side: 'left' | 'right', show: boolean) {
    this.polygonLayers[side].forEach(polygon => {
      if (show) {
        polygon.addTo(side === 'left' ? this.leftLayerGroup : this.rightLayerGroup);
      } else {
        polygon.remove();
      }
    });
  }
  clearPolygonLayer(side: string) {
    this.polygonLayers[side].forEach(polygon => polygon.remove());
    this.polygonLayers[side] = [];
  }
  createPolygonMarker(coordinates: number[], color: string, description: string, side: string) {
    const polygonPoints = [];
    for (let i = 0; i < coordinates.length; i += 3) {
      polygonPoints.push([coordinates[i + 1], coordinates[i], coordinates[i + 2]]);
    }
    const polygonLayer = L.polygon(polygonPoints, { color, weight: 6 });
    this.polygonLayers[side].push(polygonLayer);

    this.togglePolygonLayer('left', true);
    this.togglePolygonLayer('right', true);

    polygonLayer.on('click', () => {
      const markup = new DOMParser().parseFromString(description, 'text/html');
      const rows = Array.from(markup.getElementsByTagName('tr'));
      this.popupData = {};
      if (rows.length > 0) {
        rows.forEach(row => {
          const cells = row.getElementsByTagName('td');
          if (cells.length === 2) {
            const key = cells[0].innerText.trim().replace(/:$/, '');
            const valTd = cells[1];
            const imgTag = valTd.querySelector('img');
            const value = imgTag ? imgTag.src : valTd.innerText.trim();
            this.popupData[key] = value;
          }
        });
      }

      delete this.popupData['RGB_Image'];
      delete this.popupData['Thermal_Image'];

      this.popup_card_visibility = true;
    });
  }
  isImage(value: any): boolean {
    return value.match(/\.(jpeg|jpg|png)$/) !== null;
  }
  zoomreset() {
    this.map.setView([this.lat, this.long], this.main_data.zoom_level);
  }
  zoomin() {
    this.map.setZoom(this.map.getZoom() + 1);
  }
  zoomout() {
    this.map.setZoom(this.map.getZoom() - 1);
  }
  toggleFullscreen() {
    this.isFullscreen = !this.isFullscreen;
    if (!document.fullscreenElement) {
      const docElm = document.documentElement as HTMLElement;
      if (docElm.requestFullscreen) {
        docElm.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  }
  openModal(isModalOpen: boolean) {
    if (!isModalOpen) this.isModalOpen = true;
    else this.isModalOpen = false;
  }
  getData(processed_data) {
    const defects = [];
    const summary_layers = processed_data['summary_layers'] as Record<
      string,
      {
        defect_type: string;
        kml: string;
        sub_group?: Record<string, { criticality: string; kml: string; color: string }>;
        color: string;
      }
    >;
    Object.values(summary_layers).forEach(item => {
      if (item.sub_group && Object.keys(item.sub_group).length > 0) {
        Object.values(item.sub_group).forEach(sub_group_item => {
          defects.push({
            key: sub_group_item.criticality,
            kml: sub_group_item.kml,
            color: sub_group_item.color,
          });
        });
      } else {
        defects.push({
          key: item.defect_type,
          kml: item.kml,
          color: item.color,
        });
      }
    });
    return defects;
  }
  private generateMap() {
    this.map = L.map('compare_map', {
      preferCanvas: true,
      attributionControl: false,
      zoomControl: false,
      minZoom: 2,
      maxZoom: 23,
      zoomSnap: 0.5,
      twoFingerZoom: true,
      maxBoundsViscosity: 1.0,
      maxBounds: [
        [-90, -180],
        [90, 180],
      ],
    });
    this.leftLayerGroup = L.layerGroup().addTo(this.map);
    this.rightLayerGroup = L.layerGroup().addTo(this.map);
    this.zoomreset();
    this.setBaseView('terrain');
  }
  private addLayer(leftUrl: string, rightUrl: string, toggle: boolean = true) {
    if (this.map && this.base_ortho_layer) this.map.removeControl(this.base_ortho_layer);
    this.leftLayerGroup.clearLayers();
    this.rightLayerGroup.clearLayers();

    if (toggle) {
      if (new Date(this.dateleft) > new Date(this.dateright)) {
        L.tileLayer(this.ortho_file_location_left + '{z}/{x}/{y}.png', this.zoom_option).addTo(
          this.leftLayerGroup
        );
      } else {
        L.tileLayer(this.ortho_file_location_right + '{z}/{x}/{y}.png', this.zoom_option).addTo(
          this.rightLayerGroup
        );
      }
      if (this.defect_left) this.kmlName(this.defect_left, 'left');
      if (this.defect_right) this.kmlName(this.defect_right, 'right');
    }
    const myLayer1 = L.tileLayer(leftUrl + '{z}/{x}/{y}.png', this.zoom_option).addTo(
      this.leftLayerGroup
    );
    const myLayer2 = L.tileLayer(rightUrl + '{z}/{x}/{y}.png', this.zoom_option).addTo(
      this.rightLayerGroup
    );
    this.base_ortho_layer = L.control.sideBySide(myLayer1, myLayer2).addTo(this.map);
  }
  private toggleLayer(layerType: 'thermal' | 'cad' | 'dtm', enable: boolean) {
    if (enable) {
      switch (layerType) {
        case 'thermal':
          this.addLayer(this.thermal_hotspot_location_left, this.thermal_hotspot_location_right);
          break;
        case 'cad':
          if (this.isDEM) this.legendshow = true;
          this.addLayer(this.cad_file_location_left, this.cad_file_location_right);
          break;
        case 'dtm':
          this.addLayer(
            `${this.cad_location_left_plane}${this.optionleft}/`,
            `${this.cad_location_right_plane}${this.optionright}/`
          );
          break;
      }
    } else {
      if (this.legendshow) this.legendshow = false;
      if (!this.isdateleftright) this.isdateleftright = true;
      this.addLayer(this.ortho_file_location_left, this.ortho_file_location_right, false);
    }
  }
  setBaseView(option: 'terrain' | 'satellite', showMessage = false) {
    if (this.selectedImage2 === option) return;

    this.selectedImage2 = option;
    this.legendshow = false;
    if (showMessage) this.toastr.info('Please wait for a moment', 'Switching Base Map');

    const tileLayerUrl = option === 'terrain' ? this.terrain_tile_layer : this.sattelite_tile_layer;
    L.tileLayer(tileLayerUrl, this.zoom_option).addTo(this.map);
    this.addLayer(this.ortho_file_location_left, this.ortho_file_location_right, false);

    if (this.defect_left) this.kmlName(this.defect_left, 'left');
    if (this.defect_right) this.kmlName(this.defect_right, 'right');
  }
  ViewMenu(option: string) {
    this.toggleLayer('cad', false);
    this.toggleLayer('dtm', false);
    this.toggleLayer('thermal', false);
    if (this.selectedImage === option) {
      this.selectedImage = '';
      this.defect_left = '';
      this.defect_right = '';
      return;
    }
    if (option != 'comparison') {
      this.toastr.info('Please wait for a moment', 'Overlaying Requested Layer');
    }
    this.selectedImage = option;
    switch (option) {
      case 'thermal':
        this.toggleLayer('thermal', true);
        break;
      case 'cad':
        this.toggleLayer('cad', true);
        break;
      case 'dtm':
        this.isdateleftright = false;
        this.toggleLayer('dtm', true);
        break;
      case 'comparison':
        this.router.navigate(['map', 'dem-dashboard']);
        break;
      default:
        console.error('Invalid option:', option);
    }
  }
  updateSelectedDateData(date: string, side: 'left' | 'right') {
    this.toggleLayer('cad', false);
    this.toggleLayer('dtm', false);
    this.toggleLayer('thermal', false);
    this.defect_left = '';
    this.defect_right = '';
    this.selectedImage = '';
    this.isModalOpen = false;

    const processedData = this.main_data.processed_data[date];
    if (side === 'left') {
      this.ortho_file_location_left = processedData.ortho_file_location;
      this.cad_file_location_left = processedData.cad_file_location;
      this.thermal_hotspot_location_left = processedData.thermal_hotspot_location;
      this.kml_file_location_left = processedData.kml_file_location;
      this.defects_data_left = this.getData(processedData);
    } else {
      this.ortho_file_location_right = processedData.ortho_file_location;
      this.cad_file_location_right = processedData.cad_file_location;
      this.thermal_hotspot_location_right = processedData.thermal_hotspot_location;
      this.kml_file_location_right = processedData.kml_file_location;
      this.defects_data_right = this.getData(processedData);
    }
    this.addLayer(this.ortho_file_location_left, this.ortho_file_location_right, false);
  }
  updatePlanChange(value: string, side: 'left' | 'right') {
    if (side === 'left') {
      this.optionleft = this.planMapping[value];
      this.legendleft = `${this.commonlegendUrl}${this.legendMapping[value]}.png`;
    } else {
      this.optionright = this.planMapping[value];
      this.legendright = `${this.commonlegendUrl}${this.legendMapping[value]}.png`;
    }
    this.isdateleftright = false;
    this.toggleLayer('dtm', true);
  }
}
