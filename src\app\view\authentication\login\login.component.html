<div class="inner_login">
  <div class="login-cont">
    <div class="my-3">
      <div class="info_heading">Sign in to your Account</div>
      <div class="info_text mt-2">Please use an organization email id only.</div>
    </div>
    <div>
      <button
        mat-mini-fab
        matTooltip="Google"
        matTooltipPosition="above"
        class="login-with-btn google"
        (click)="loginWithSocial('google')"></button>
      <button
        mat-mini-fab
        matTooltip="Microsoft"
        matTooltipPosition="above"
        class="login-with-btn microsoft"
        (click)="loginWithSocial('microsoft')"></button>
      <button
        mat-mini-fab
        matTooltip="Email"
        matTooltipPosition="above"
        class="login-with-btn email"
        (click)="showEmailLogin = !showEmailLogin"></button>
    </div>
    <ng-container *ngIf="showEmailLogin">
      <div class="info_text my-3">------ or, Sign in with Email ------</div>
      <div class="text-center">
        <form [formGroup]="loginForm">
          <mat-form-field appearance="outline" class="w-75">
            <mat-label class="field-content">Enter your email</mat-label>
            <mat-icon matPrefix class="field-icon">person</mat-icon>
            <input
              matInput
              placeholder="Enter your email"
              name="username"
              #UserName
              formControlName="username"
              required />
            <mat-error *ngIf="hasError('username', 'required')">Email is required</mat-error>
          </mat-form-field>
          <mat-form-field appearance="outline" class="w-75 mt-2">
            <mat-label class="field-content">Enter your password</mat-label>
            <mat-icon matPrefix class="field-icon">vpn_key</mat-icon>
            <input
              matInput
              [type]="hide ? 'password' : 'text'"
              name="UserName"
              #Password
              formControlName="password"
              required />
            <button
              mat-icon-button
              matSuffix
              (click)="hide = !hide"
              [attr.aria-label]="'Hide password'"
              [attr.aria-pressed]="hide"
              class="field-icon">
              <mat-icon>{{ hide ? 'visibility_off' : 'visibility' }}</mat-icon>
            </button>
            <mat-error *ngIf="hasError('password', 'required')">Password is required</mat-error>
          </mat-form-field>
        </form>
        <button
          mat-button
          matTooltip="Login"
          matTooltipPosition="above"
          class="btn stepper-action-btn text-uppercase w-25 mt-1"
          [disabled]="!loginForm.valid || Isworking"
          (click)="loginWithEmail()">
          <span *ngIf="!Isworking">Login</span>
          <span *ngIf="Isworking">
            <div class="spinner-border" role="status" *ngIf="Isworking"></div>
          </span>
        </button>
      </div>
    </ng-container>
    <div class="my-3">
      <a [routerLink]="['/auth', 'register']" class="ml-5 mr-5 margin-set font-weight-bold">
        Sign Up
      </a>
      <a [routerLink]="['/auth', 'reset-password']" class="ml-5 mr-5 margin-set font-weight-bold">
        Forgot password?
      </a>
    </div>
    <div class="info_text my-1">
      By clicking on <span class="font-weight-bolder">"login or sign up"</span> above, you
      acknowledge that <br />you have read and understood, and agree to
      <span class="font-weight-bolder">Datasee.ai's</span><br />
      <a [routerLink]="['/auth', 'login']" class="font-weight-bolder">Terms & Conditions</a> and
      <a [routerLink]="['/auth', 'login']" class="font-weight-bolder">Privacy Policy</a>.
    </div>
  </div>
  <div class="logo-cont">
    <div class="my-3 width-set">
      <img src="{{ logo }}" class="w-100" />
    </div>
    <div class="my-3">
      <div class="label-text">Trusted by Industry Leaders ...</div>
      <div *ngFor="let row of clientRows" class="flex-col my-3">
        <img *ngFor="let img of row" [src]="client_logo + img + '.png'" />
      </div>
    </div>
    <div class="w-100 mb-3">
      <div class="label-text mb-2">Software for Solar Projects in ...</div>
      <div class="flex-col bottom-set" style="color: black">
        <div class="feature-text"><mat-icon>done</mat-icon> <span>Development</span></div>
        <div class="feature-text"><mat-icon>done</mat-icon> <span>Construction</span></div>
        <div class="feature-text"><mat-icon>done</mat-icon> <span>Operations</span></div>
      </div>
    </div>
  </div>
</div>
