<section class="price-area" id="price">
  <section id="pricing" class="pos-r">
    <canvas id="canvas" width="1519" height="1018"></canvas>
    <div class="container">
      <div class="row text-center">
        <div class="col-lg-8 col-md-12 ml-auto mr-auto">
          <div class="section-title">
            <mat-icon> attach_money</mat-icon>
            <!-- <div class="title-effect">
                            <div class="bar bar-top"></div>
                            <div class="bar bar-right"></div>
                            <div class="bar bar-bottom"></div>
                            <div class="bar bar-left"></div>
                        </div> -->
            <h5>Pricing Plan</h5>
            <h2 class="title">Choose affordable prices</h2>
          </div>
        </div>
      </div>
      <div class="row align-items-center">
        <div class="col-lg-4 col-md-12">
          <div class="price-table">
            <div class="price-inside">Premium</div>
            <div class="price-header">
              <div class="price-value">
                <h2><span>$</span>99</h2>
                <span>Monthly Package</span>
              </div>
              <h3 class="price-title">Premium User</h3>
            </div>
            <a class="btn btn-theme btn-circle my-4" (click)="next()" data-text="Purchase Now">
              <span>P</span><span>u</span><span>r</span><span>c</span><span>h</span><span>a</span
              ><span>s</span><span>e</span> <span> </span><span>N</span><span>o</span><span>w</span>
            </a>
            <div class="price-list">
              <ul class="list-unstyled">
                <li>Thermographic Inspection Report</li>
                <li>Hotspots classification as per IEC 62446-3</li>
                <li>Raw Thermal Images of Defective Panels</li>
                <li>Power Loss Estimation for every hotspot</li>
                <li>Geo-referenced CAD Overlay</li>
                <li>Interactive Editable features for Panel Replacement</li>
                <li>Hierarchy based User profiling</li>
                <li>Georeferenced overlay of Thermal Images with details</li>
                <li>Free Data Storage up to 300MW</li>
                <li>Interactive UI to annotate & tag AOIs</li>
                <li>Automatic processing Number of Users: 5</li>
                <li>Unlimited File downloads</li>
                <li>Export to Third party software</li>
                <li>Automatic Feature Updates</li>
                <li>Machine Learning Features</li>
                <li>Platform Training & Support</li>
                <li>Unlimited Takvaviya cloud access</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="col-lg-4 col-md-12">
          <div class="price-table custom-position">
            <div class="price-inside">Custom</div>
            <div class="price-header">
              <div class="price-value">
                <h2><span>$</span>100</h2>
                <span>Monthly Package</span>
              </div>
              <h3 class="price-title">Custom User</h3>
            </div>
            <a class="btn btn-theme btn-circle my-4" (click)="next()" data-text="Purchase Now">
              <span>P</span><span>u</span><span>r</span><span>c</span><span>h</span><span>a</span
              ><span>s</span><span>e</span> <span> </span><span>N</span><span>o</span><span>w</span>
            </a>
            <div class="price-list">
              <ul class="list-unstyled">
                <li>Thermographic Inspection Report</li>
                <li>Hotspots Classification as per IEC 62446-3</li>
                <li>Raw Thermal Images of Defective panels</li>
                <li>Georeferenced overlay of Thermal Images with details</li>
                <li>Free Data Storage up to 100MW</li>
                <li>Interactive UI to annotate & tag AOIs</li>
                <li>Automatic processing</li>
                <li>Number of Users: 5</li>
                <li>Unlimited File downloads</li>
                <li>Export to Third party software</li>
                <li>Automatic Feature Updates</li>
                <li>Machine Learning Features</li>
                <li>Platform Training & Support</li>
                <li>Unlimited Takvaviya cloud access</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="col-lg-4 col-md-12">
          <div class="price-table custom-position">
            <div class="price-inside">Standard</div>
            <div class="price-header">
              <div class="price-value">
                <h2><span>$</span>99</h2>
                <span>Monthly Package</span>
              </div>
              <h3 class="price-title">Standard User</h3>
            </div>
            <a class="btn btn-theme btn-circle my-4" (click)="next()" data-text="Purchase Now">
              <span>P</span><span>u</span><span>r</span><span>c</span><span>h</span><span>a</span
              ><span>s</span><span>e</span> <span> </span><span>N</span><span>o</span><span>w</span>
            </a>
            <div class="price-list">
              <ul class="list-unstyled">
                <li>Thermographic Inspection Report</li>
                <li>Hotspots Classification as per IEC 62446-3</li>
                <li>Raw Thermal Images of Defective panels</li>
                <li>Georeferenced overlay of Thermal Images with details</li>
                <li>Free Data Storage up to 100MW</li>
                <li>Interactive UI to annotate & tag AOIs</li>
                <li>Automatic processing</li>
                <li>Number of Users: 5</li>
                <li>Unlimited File downloads</li>
                <li>Export to Third party software</li>
                <li>Automatic Feature Updates</li>
                <li>Machine Learning Features</li>
                <li>Platform Training & Support</li>
                <li>Unlimited Takvaviya cloud access</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</section>
<script>
  var Canvas = document.getElementById('canvas');
  var ctx = Canvas.getContext('2d');

  var resize = function () {
    Canvas.width = Canvas.clientWidth;
    Canvas.height = Canvas.clientHeight;
  };
  window.addEventListener('resize', resize);
  resize();

  var elements = [];
  var presets = {};

  presets.o = function (x, y, s, dx, dy) {
    return {
      x: x,
      y: y,
      r: 12 * s,
      w: 5 * s,
      dx: dx,
      dy: dy,
      draw: function (ctx, t) {
        this.x += this.dx;
        this.y += this.dy;

        ctx.beginPath();
        ctx.arc(
          this.x + +Math.sin((50 + x + t / 10) / 100) * 3,
          this.y + +Math.sin((45 + x + t / 10) / 100) * 4,
          this.r,
          0,
          2 * Math.PI,
          false
        );
        ctx.lineWidth = this.w;
        ctx.strokeStyle = '#f44d85';
        ctx.stroke();
      },
    };
  };

  presets.x = function (x, y, s, dx, dy, dr, r) {
    r = r || 0;
    return {
      x: x,
      y: y,
      s: 20 * s,
      w: 5 * s,
      r: r,
      dx: dx,
      dy: dy,
      dr: dr,
      draw: function (ctx, t) {
        this.x += this.dx;
        this.y += this.dy;
        this.r += this.dr;

        var _this = this;
        var line = function (x, y, tx, ty, c, o) {
          o = o || 0;
          ctx.beginPath();
          ctx.moveTo(-o + (_this.s / 2) * x, o + (_this.s / 2) * y);
          ctx.lineTo(-o + (_this.s / 2) * tx, o + (_this.s / 2) * ty);
          ctx.lineWidth = _this.w;
          ctx.strokeStyle = c;
          ctx.stroke();
        };

        ctx.save();

        ctx.translate(
          this.x + Math.sin((x + t / 10) / 100) * 5,
          this.y + Math.sin((10 + x + t / 10) / 100) * 2
        );
        ctx.rotate((this.r * Math.PI) / 1500);

        line(-1, -1, 1, 1, '#f44d85');
        line(1, -1, -1, 1, '#481ea7');

        ctx.restore();
      },
    };
  };

  for (var x = 0; x < Canvas.width; x++) {
    for (var y = 0; y < Canvas.height; y++) {
      if (Math.round(Math.random() * 25000) == 1) {
        var s = (Math.random() * 5 + 1) / 10;
        if (Math.round(Math.random()) == 1) elements.push(presets.o(x, y, s, 0, 0));
        else
          elements.push(
            presets.x(x, y, s, 0, 0, (Math.random() * 3 - 1) / 10, Math.random() * 360)
          );
      }
    }
  }

  setInterval(function () {
    ctx.clearRect(0, 0, Canvas.width, Canvas.height);

    var time = new Date().getTime();
    for (var e in elements) elements[e].draw(ctx, time);
  }, 10);
</script>
