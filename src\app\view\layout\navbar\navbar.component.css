::ng-deep .mat-toolbar-row,
.mat-toolbar-single-row {
  position: fixed;
  padding: 1.5rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 1200;
  background-color: var(--white);
}
.logo {
  width: 12.5rem;
  height: 3.125rem;
  padding: 4px;
}
.menu-section {
  position: absolute;
  right: 100px;
  top: 8px;
}
.menu-section .quick-menu {
  display: inline-block;
  height: 100%;
}

.menu-section .icon-category {
  position: relative;
  top: 8px;
}

.menu-section .icon-category .account {
  font-size: 13px !important;
}

.menu-section .icon-category .account .menu-btn {
  font-size: 14px !important;
}

.menu-toggle {
  position: absolute;
  right: 15px;
}

.sidemenu-header {
  padding: 10px 12px;
}

.menu-icon {
  font-size: 18px;
}

.menu-text {
  top: -2px;
  left: 6px;
  font-size: 14px;
  position: relative;
}

.border-btn {
  border: 1px solid;
}

.user-details {
  position: relative;
  display: inline-block;
}

.user-details .user-avatar {
  width: 40px;
  height: 40px;
  background-color: var(--primary);
  border-radius: 50%;
  position: relative;
}

.user-avatar .username-f-letter {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-wrap: wrap;
  align-content: center;
  color: var(--white);
  font-size: 20px;
  font-weight: 600;
  text-transform: uppercase;
}

.user-details .user-info {
  display: inline-block;
  margin-left: 15px;
}

.user-info .username {
  position: relative;
  top: 10px;
  letter-spacing: 0.5px;
  text-align: left;
}

.user-info .role {
  position: relative;
  top: -6px;
  font-size: 12px;
  font-weight: 400;
  text-align: left;
}
