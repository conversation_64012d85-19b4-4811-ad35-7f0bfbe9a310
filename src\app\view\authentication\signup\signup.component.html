<div *ngIf="data_verify; then thenBlock; else elseBlock"></div>
<ng-template #thenBlock>
  <form [formGroup]="signupForm">
    <mat-card class="signup-card" style="width: 95%">
      <mat-card-content>
        <div class="text-uppercase text-center mt-4 font-weight-bolder fs-3">
          Create a new Account
        </div>
        <div class="signup-form">
          <mat-form-field appearance="outline" class="w-25 p-2">
            <mat-label class="field-content">Email Address</mat-label>
            <input matInput name="email" formControlName="email" />
            <mat-error *ngIf="hasError('email', 'required')">Email is required</mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="w-25 p-2">
            <mat-label class="field-content">First Name</mat-label>
            <input
              matInput
              name="first_name"
              formControlName="first_name"
              (input)="trimValue('first_name')" />
            <mat-error *ngIf="hasError('first_name', 'required')">First name is required</mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="w-25 p-2">
            <mat-label class="field-content">Last Name</mat-label>
            <input
              matInput
              name="last_name"
              formControlName="last_name"
              (input)="trimValue('last_name')" />
            <mat-error *ngIf="hasError('last_name', 'required')">Last name is required</mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="w-25 p-2">
            <mat-label class="field-content">Mobile Number</mat-label>
            <input matInput name="contact" formControlName="contact" type="number" min="0" />
            <mat-error *ngIf="hasError('contact', 'required')">Mobile number is required</mat-error>
            <mat-error *ngIf="hasError('contact', 'pattern')">Mobile number is invalid</mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="w-25 p-2">
            <mat-label class="field-content">Organization Name</mat-label>
            <input
              matInput
              name="organization"
              formControlName="organization"
              (input)="trimValue('organization')" />
            <mat-error *ngIf="hasError('organization', 'required')">
              Organization is required
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="w-25 p-2">
            <mat-label class="field-content">Country</mat-label>
            <mat-select
              placeholder="country"
              (selectionChange)="selectCountry($event.value)"
              name="country"
              formControlName="country">
              <mat-option value="" selected>Select Country</mat-option>
              <mat-option *ngFor="let country of countries" [value]="country.isoCode">
                {{ country.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="hasError('country', 'required')">Country is required</mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="w-25 p-2">
            <mat-label class="field-content">State</mat-label>
            <mat-select
              placeholder="state"
              (selectionChange)="selectState($event.value)"
              name="state"
              formControlName="state">
              <mat-option value="" selected>Select State</mat-option>
              <mat-option *ngFor="let state of states_data" [value]="state.isoCode">
                {{ state.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="hasError('state', 'required')">State is required</mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="w-25 p-2">
            <mat-label class="field-content">City</mat-label>
            <mat-select placeholder="city" name="city" formControlName="city">
              <mat-option value="" selected>Select City</mat-option>
              <mat-option *ngFor="let city_data of cities_data" [value]="city_data.name">
                {{ city_data.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="hasError('city', 'required')">City is required</mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="w-25 p-2">
            <mat-label class="field-content">Pincode</mat-label>
            <input matInput name="pincode" formControlName="pincode" type="number" min="0" />
            <mat-error *ngIf="hasError('pincode', 'required')">Pincode is required</mat-error>
            <mat-error *ngIf="hasError('pincode', 'pattern')">Pincode is invalid</mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="w-25 p-2">
            <mat-label class="field-content">Password</mat-label>
            <input
              matInput
              [type]="hide ? 'password' : 'text'"
              name="password"
              formControlName="password"
              (input)="trimValue('password'); checkPassword()" />
            <button mat-icon-button matSuffix (click)="hide = !hide" class="field-icon">
              <mat-icon>{{ hide ? 'visibility_off' : 'visibility' }}</mat-icon>
            </button>
            <mat-error *ngIf="hasError('password', 'required')">Password is required</mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="w-25 p-2">
            <mat-label class="field-content">Confirm password</mat-label>
            <input
              matInput
              [type]="hide2 ? 'password' : 'text'"
              name="cpassword"
              formControlName="cpassword"
              (input)="trimValue('cpassword'); checkPassword()" />
            <button mat-icon-button matSuffix (click)="hide2 = !hide2" class="field-icon">
              <mat-icon>{{ hide2 ? 'visibility_off' : 'visibility' }}</mat-icon>
            </button>
            <mat-error *ngIf="hasError('cpassword', 'required')">
              Confirm password is required
            </mat-error>
            <mat-hint *ngIf="!isValidPassword" style="color: red">
              Password and Confirm password doesn't match
            </mat-hint>
          </mat-form-field>
        </div>
        <div class="signup-btn-container">
          <button
            mat-button
            class="btn stepper-action-btn text-uppercase mt-1"
            [disabled]="Isworking"
            (click)="signup()">
            <span *ngIf="!Isworking">signup</span>
            <span *ngIf="Isworking">
              <div class="spinner-border" role="status" *ngIf="Isworking"></div>
            </span>
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  </form>
</ng-template>
<ng-template #elseBlock>
  <form [formGroup]="verifyForm">
    <mat-card class="signup-card">
      <mat-card-content>
        <div class="user-container">
          <mat-icon class="user-icon">account_circle</mat-icon>
        </div>
        <div class="text-uppercase text-center mt-4 font-weight-bolder fs-3">Create Account</div>
        <div class="mt-4" style="display: flex; flex-direction: column; align-items: center">
          <mat-form-field appearance="outline" class="w-75 mt-2">
            <mat-label class="field-content">Enter your email</mat-label>
            <mat-icon matPrefix class="field-icon">mail</mat-icon>
            <input
              type="email"
              matInput
              name="email"
              placeholder="Enter your email"
              formControlName="email"
              (input)="applyPattern('email')" />
            <mat-error *ngIf="hasError1('email', 'required')">Email is required</mat-error>
            <mat-error *ngIf="hasError1('email', 'pattern')">Email is invalid</mat-error>
          </mat-form-field>
        </div>
      </mat-card-content>
      <div class="text-center mb-4 mt-2">
        <button
          mat-button
          class="btn stepper-action-btn text-uppercase mt-1"
          [disabled]="!verifyForm.valid || Isworking"
          (click)="verify()">
          <span *ngIf="!Isworking">Verify</span>
          <span *ngIf="Isworking">
            <div class="spinner-border" role="status" *ngIf="Isworking"></div>
          </span>
        </button>
      </div>
      <div class="action-container">
        Already Have an account?&nbsp;
        <a class="signup-text" [routerLink]="['/auth', 'login']"><b>Login</b></a>
      </div>
    </mat-card>
  </form>
</ng-template>
