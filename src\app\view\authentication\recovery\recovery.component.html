<form [formGroup]="loginForm">
  <mat-card class="signup-card">
    <mat-card-content>
      <div class="user-container">
        <mat-icon class="user-icon">vpn_key</mat-icon>
      </div>
      <div class="text-uppercase text-center mt-4 font-weight-bolder fs-3">Password Reset</div>
      <div class="mt-4" style="display: flex; flex-direction: column; align-items: center">
        <mat-form-field appearance="outline" class="w-75">
          <mat-label class="field-content">Enter new password</mat-label>
          <mat-icon matPrefix class="field-icon">vpn_key</mat-icon>
          <input
            matInput
            placeholder="Enter your new password"
            [type]="hide1 ? 'password' : 'text'"
            name="password"
            formControlName="password"
            (input)="checkPassword()" />
          <button
            mat-icon-button
            matSuffix
            (click)="hide1 = !hide1"
            (input)="checkPassword()"
            class="field-icon">
            <mat-icon>{{ hide1 ? 'visibility_off' : 'visibility' }}</mat-icon>
          </button>
          <mat-error *ngIf="hasError('password', 'required')"> Password is required </mat-error>
        </mat-form-field>
        <mat-form-field appearance="outline" class="w-75 mt-2">
          <mat-label class="field-content">Confirm new password</mat-label>
          <mat-icon matPrefix class="field-icon">vpn_key</mat-icon>
          <input
            matInput
            placeholder="Confirm new password"
            [type]="hide2 ? 'password' : 'text'"
            name="cpassword"
            formControlName="cpassword"
            (input)="checkPassword()" />
          <button
            mat-icon-button
            matSuffix
            (click)="hide2 = !hide2"
            (input)="checkPassword()"
            class="field-icon">
            <mat-icon>{{ hide2 ? 'visibility_off' : 'visibility' }}</mat-icon>
          </button>
          <mat-error *ngIf="hasError('cpassword', 'required')"
            >Confirm Password is required
          </mat-error>
          <mat-hint *ngIf="!isValidPassword" style="color: red">
            Password and Confirm password doesn't match
          </mat-hint>
        </mat-form-field>
      </div>
    </mat-card-content>
    <div class="text-center my-2">
      <div class="text-center">
        <button
          mat-button
          class="w-25 text-uppercase"
          (click)="resetPassword()"
          [disabled]="!loginForm.valid || Isworking">
          <span *ngIf="!Isworking">Reset</span>
          <span *ngIf="Isworking">
            <div class="spinner-border" role="status" *ngIf="Isworking"></div>
          </span>
        </button>
      </div>
    </div>
  </mat-card>
</form>
