import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { PlotlyModule } from 'angular-plotly.js';
import * as PlotlyJS from 'plotly.js-dist-min';
import { MaterialLibrary } from 'src/app/library/material.lib';
import { LayoutModule } from '../layout/layout.module';
import { PageNotFoundComponent } from '../page-not-found/page-not-found.component';
import { AnalyticsComponent } from './analytics/analytics.component';
import { DefectRectificationComponent } from './defect-rectification/defect-rectification.component';
import { ManageuserComponent } from './manageuser/manageuser.component';
import { MyprofileComponent } from './myprofile/myprofile.component';
import { PowerlossDashboardComponent } from './powerloss-dashboard/powerloss-dashboard.component';
import { ProfileModalComponent } from './profile-modal/profile-modal.component';
import { SectionRoutingModule } from './section-routing.module';
import { SectionComponent } from './section.component';

PlotlyModule.plotlyjs = PlotlyJS;

@NgModule({
  declarations: [
    SectionComponent,
    AnalyticsComponent,
    MyprofileComponent,
    ManageuserComponent,
    ProfileModalComponent,
    PageNotFoundComponent,
    DefectRectificationComponent,
    PowerlossDashboardComponent,
  ],
  imports: [
    CommonModule,
    SectionRoutingModule,
    LayoutModule,
    MaterialLibrary,
    RouterModule,
    PlotlyModule,
    ReactiveFormsModule,
    FormsModule,
  ],
})
export class SectionModule {}
