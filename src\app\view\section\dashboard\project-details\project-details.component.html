<div *ngIf="get_dashboard_data" class="display-flex" style="padding: 1rem; margin-top: 70px">
  <div class="stats-container">
    <div class="card2 card1">
      <div class="stats-card">
        <div>
          <p class="box-head">Total capacity scanned</p>
        </div>
        <div>
          <p class="box-text">{{ get_dashboard_data.plant_size_scanned }}</p>
        </div>
        <div>
          <p class="box-sm">Inspected till date</p>
        </div>
      </div>
      <div class="btn-place">
        <button mat-fab style="background-color: var(--primary) !important">
          <mat-icon>widgets</mat-icon>
        </button>
      </div>
    </div>
  </div>
  <div class="stats-container">
    <div class="card2 card1">
      <div class="stats-card">
        <div>
          <p class="box-head">Total Net Power Loss</p>
        </div>
        <div>
          <p class="box-text">{{ get_dashboard_data.total_power_loss }}</p>
        </div>
        <div>
          <p class="box-sm">Estimated Power Loss</p>
        </div>
      </div>
      <div class="btn-place" style="background-color: #f2f2f2">
        <button mat-fab style="background-color: var(--primary) !important">
          <mat-icon>flash_on</mat-icon>
        </button>
      </div>
    </div>
  </div>
  <div class="stats-container">
    <div class="card2 card1">
      <div class="stats-card">
        <div>
          <p class="box-head">Total Number of Issues</p>
        </div>
        <div>
          <p class="box-text">{{ get_dashboard_data.total_defects }}</p>
        </div>
        <div>
          <p class="box-sm">Issues detected</p>
        </div>
      </div>
      <div class="btn-place">
        <button mat-fab style="background-color: var(--primary) !important">
          <mat-icon>device_hub</mat-icon>
        </button>
      </div>
    </div>
  </div>
</div>
<ng-container *ngIf="recent_3_projects">
  <p class="header-text">Recent Projects</p>
  <div class="row" style="margin: 1rem">
    <div class="col-4 my-2" *ngFor="let project of recent_3_projects; let i = index">
      <div class="active-project" title="Active projects" (click)="Clickable_recent3(i)">
        <div class="col-12">
          <img class="project-image" src="{{ project.image }}" style="width: 25%" />
        </div>
        <div class="col-12">
          <div class="project-active-details">
            <div
              class="project-title"
              style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap">
              <p style="font-weight: 600">
                {{ project.name }}
              </p>
            </div>
            <div class="project-date">
              <p>{{ project.date[0] }}</p>
            </div>
            <div class="project-state">
              <p>{{ project.city }}</p>
            </div>
            <div *ngIf="project.status[0] == 'created' || project.status[0] == 'Created'">
              <div
                class="progress-bar progress-bar-striped progress-bar-animated bg-success"
                role="progressbar"
                [style.width]="'25%'">
                25%
              </div>
            </div>
            <div *ngIf="project.status[0] == 'ftp' || project.status[0] == 'FTP'">
              <div
                class="progress-bar progress-bar-striped progress-bar-animated bg-success"
                role="progressbar"
                [style.width]="'50%'">
                50%
              </div>
            </div>
            <div *ngIf="project.status[0] == 'processing' || project.status[0] == 'Processing'">
              <div
                class="progress-bar progress-bar-striped progress-bar-animated bg-success"
                role="progressbar"
                [style.width]="'75%'">
                75%
              </div>
            </div>
            <div
              *ngIf="
                project.status[0] == 'completed' ||
                project.status[0] == 'Complete' ||
                project.status[0] == 'Completed'
              ">
              <div class="progress">
                <div
                  class="progress-bar progress-bar-striped progress-bar-animated bg-success"
                  role="progressbar"
                  [style.width]="'100%'">
                  100%
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="project-container">
    <button mat-mini-fab class="prev mat-elevation-z8" (click)="plusSlides(-1)">
      <mat-icon class="icon" style="margin-top: 10px">keyboard_arrow_left</mat-icon>
    </button>
    <button mat-mini-fab class="next mat-elevation-z8" (click)="plusSlides(1)">
      <mat-icon class="icon" style="margin-top: 10px">keyboard_arrow_right</mat-icon>
    </button>
    <div class="project-card">
      <div class="horizontal-card">
        <div class="title">
          {{ project_number }}
        </div>
        <div class="project-details">
          <div class="project-title">
            <h5>
              <p style="font-weight: 600">
                {{ recent_3_projects_name }}
              </p>
            </h5>
          </div>
          <div class="project-date">
            <p>{{ recent_3_projects_date }}</p>
          </div>
          <div *ngIf="workflow_status == 'created' || workflow_status == 'Created'">
            <div
              class="progress-bar progress-bar-striped progress-bar-animated bg-success"
              role="progressbar"
              [style.width]="'25%'">
              25%
            </div>
          </div>
          <div *ngIf="workflow_status == 'ftp' || workflow_status == 'FTP'">
            <div
              class="progress-bar progress-bar-striped progress-bar-animated bg-success"
              role="progressbar"
              [style.width]="'50%'">
              50%
            </div>
          </div>
          <div *ngIf="workflow_status == 'processing' || workflow_status == 'Processing'">
            <div
              class="progress-bar progress-bar-striped progress-bar-animated bg-success"
              role="progressbar"
              [style.width]="'75%'">
              75%
            </div>
          </div>
          <div
            *ngIf="
              workflow_status == 'completed' ||
              workflow_status == 'Complete' ||
              workflow_status == 'Completed'
            ">
            <div
              class="progress-bar progress-bar-striped progress-bar-animated bg-success"
              role="progressbar"
              [style.width]="'100%'">
              100%
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="container stepper">
      <div class="title title-imp">
        <mat-icon class="icon" style="margin: 0px">stars</mat-icon>
        Workflow Status
      </div>
      <div class="steps">
        <mat-vertical-stepper #stepper>
          <mat-step label="Project Creation">
            <ng-template matStepperIcon="edit">
              <mat-icon>done</mat-icon>
            </ng-template>
          </mat-step>
          <mat-step label="Data-Acquistion"></mat-step>
          <mat-step label="Data Processing"></mat-step>
          <mat-step label="Analytics & Visualization"></mat-step>
        </mat-vertical-stepper>
        <div class="custom-btn">
          <button mat-button class="mr-4" (click)="gotomap()">Map</button>
          <button mat-button (click)="gotoAnalytics()">Analytics</button>
        </div>
      </div>
    </div>
  </div>
</ng-container>
