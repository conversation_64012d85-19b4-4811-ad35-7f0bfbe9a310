@import url("https://cdn.jsdelivr.net/gh/digidem/leaflet-side-by-side/layout.css");
@import url("https://cdn.jsdelivr.net/gh/digidem/leaflet-side-by-side/range.css");

#compare_map {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
.dtmlegend {
  position: absolute;
  top: 350px;
  z-index: 1000;
}
.dtmlegend img {
  height: 200px;
  border-radius: 5px;
  border-left: solid 3px var(--primary);
}
.dtmlegend img:hover {
  cursor: move;
}
.datebuttonleft {
  position: fixed;
  top: 5%;
  left: 10%;
  z-index: 1000;
}
.statusbuttonleft {
  position: fixed;
  left: 5%;
  bottom: 5%;
  z-index: 1000;
}
.datebuttonright {
  position: fixed;
  right: 10%;
  top: 5%;
  z-index: 1000;
}
.statusbuttonright {
  position: fixed;
  right: 5%;
  bottom: 5%;
  z-index: 1000;
}
@media only screen and (max-width: 720px) {
  .bottom-container {
    display: block !important;
  }
}
.status-indication {
  display: flex;
  align-items: center;
  justify-content: space-around;
  width: 175px;
  padding: 5px;
}
.status-indication-all {
  display: flex;
  flex-direction: column;
  gap: 5px;
  width: 175px;
  padding: 5px;
}
@media (max-width: 360px) {
  .status-indication,
  .status-indication-all {
    width: 120px;
    height: fit-content;
  }
}
.status-indication-all .indication-bar {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}
.status-indication .indication-bar {
  width: 15px;
  height: 15px;
  border-radius: 50%;
}
.status-indication-all .indication-text {
  font-size: 10px;
  font-weight: 600;
  text-transform: capitalize;
}
.status-indication .indication-text {
  font-size: 14px;
  font-weight: 600;
  text-transform: capitalize;
}
.close-btn {
  background-color: var(--primary) !important;
  left: 0.625rem;
  top: 0.625rem;
  position: absolute;
  color: var(--white);
  z-index: 9999 !important;
}
@media (max-width: 550px) {
  .select-text {
    width: 100px;
    height: fit-content;
  }
}
.button-center {
  position: fixed;
  left: 3.75rem;
  display: flex;
  justify-content: space-between;
  width: 80%;
  align-items: center;
  top: 0.625rem;
  right: 3.75rem;
  z-index: 1000;
  margin: auto auto;
}
.fullscreen {
  top: 50px;
}
.reset {
  top: 100px;
}
.in {
  top: 150px;
}
.out {
  top: 200px;
}
.anomalies {
  top: 250px;
}
.stacks {
  top: 300px;
}
.icons-placing0 {
  top: 350px;
}
.icons-placing1 {
  top: 400px;
}
.icons-placing2 {
  top: 450px;
}
.side-icons {
  font-size: 28px;
  right: 10px;
  padding: 5px;
  background-color: var(--white);
  width: 40px;
  height: 40px;
  position: fixed;
  z-index: 1000;
  cursor: pointer;
  border-radius: 8px;
  border: solid 2px var(--primary);
}
.compare-two {
  margin-top: 75px;
  font-size: 12px;
  width: 300px;
  position: fixed;
  right: 55px;
  z-index: 1000;
  border-radius: 15px;
}
@media (max-height: 450px) {
  .compare-two {
    width: 50%;
    height: 300px;
    overflow-y: scroll;
  }
}
@media (min-width: 700px) {
  .compare-two {
    width: 26%;
  }
}
.mapslayout {
  top: 300px;
  right: 55px;
  position: fixed;
  z-index: 1200;
  background-color: var(--white);
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  border-radius: 15px;
  border: solid 3px var(--primary);
  width: 150px;
}
.drone-data-thermal {
  display: flex;
  flex-direction: column;
  border-bottom: solid 1px grey;
}
.base-map {
  display: flex;
  flex-direction: column;
  height: fit-content;
}
.base-map-img {
  display: flex;
  justify-content: space-evenly;
  z-index: 1000;
  gap: 10px;
  padding: 5px;
}
.span-font {
  font-size: 0.9rem;
  font-weight: 600;
  text-align: center;
  margin: 5px 0px;
}
.img-items {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.img-dim {
  height: 50px;
  width: 50px;
  border-radius: 10px;
  border-style: solid;
  border-width: 3px;
  background: whitesmoke;
}
.img-dim:hover {
  cursor: pointer;
}
.comparison-card {
  background-color: var(--white);
  font-size: 12px;
  border-radius: 15px;
  width: 300px;
  border: solid 3px var(--primary);
  position: absolute;
  bottom: 0px;
  right: 55px;
  padding: 5px;
  text-align: center;
}
.icon-wrapper {
  width: 25px;
  height: 25px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}
.icon-wrapper img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  cursor: pointer;
}
